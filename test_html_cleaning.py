#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试HTML清理功能
"""

import sys
sys.path.append('.')
from convert_de_to_woocommerce import GermanWooCommerceConverter

def test_html_cleaning():
    converter = GermanWooCommerceConverter()
    
    # 测试问题HTML内容
    test_cases = [
        {
            'name': '德语单词包含on',
            'html': 'Zanussi Konvektionsofen EK / 20xGN1/1, Standgerät, für GN 1/1, 406240208 mit Quesauberschub',
            'should_clean': False
        },
        {
            'name': '真正的onclick事件',
            'html': '<p onclick="alert()">危险的事件处理器</p>',
            'should_clean': True
        },
        {
            'name': '真正的onload事件',
            'html': '<div onload="hack()">另一个危险事件</div>',
            'should_clean': True
        },
        {
            'name': '德语产品名称',
            'html': 'Hammerbacher FlexWall Schubladen 80 cm, 1 OH, BS, Asteiche, VFWS28/R/BS',
            'should_clean': False
        },
        {
            'name': '正常HTML',
            'html': '<p>正常的HTML内容 mit deutschen Wörtern</p>',
            'should_clean': False
        },
        {
            'name': 'script标签',
            'html': '<script>alert("hack")</script><p>内容</p>',
            'should_clean': True
        }
    ]
    
    print('HTML清理功能测试:')
    print('='*50)
    
    for i, case in enumerate(test_cases, 1):
        original = case['html']
        cleaned = converter.clean_german_html(original)
        
        print(f'{i}. {case["name"]}:')
        print(f'   原始: {original}')
        print(f'   清理: {cleaned}')
        
        # 检查是否按预期清理
        if case['should_clean']:
            if original == cleaned:
                print('   ❌ 应该被清理但没有被清理')
            else:
                print('   ✅ 正确清理')
        else:
            if original != cleaned:
                print('   ⚠️  不应该被修改但被修改了')
            else:
                print('   ✅ 正确保留')
        print()

if __name__ == "__main__":
    test_html_cleaning()
