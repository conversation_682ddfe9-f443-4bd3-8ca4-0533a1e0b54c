#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进版Profishop Tags优化器 - 保留真正的核心产品关键词
"""

import pandas as pd
import re
from pathlib import Path
from collections import Counter

class ImprovedProfishopOptimizer:
    def __init__(self):
        """初始化改进版优化器"""
        
        # 产品功能关键词 (最高优先级)
        self.function_keywords = {
            # 工具类
            'bohrer': 'drill',
            'bohrmaschine': 'drilling_machine', 
            'säge': 'saw',
            'kreissäge': 'circular_saw',
            'stichsäge': 'jigsaw',
            'schrauber': 'screwdriver',
            'schlagschrauber': 'impact_driver',
            'hammer': 'hammer',
            'schlagbohrer': 'hammer_drill',
            'zange': 'pliers',
            'messer': 'knife',
            'cutter': 'cutter',
            'schlüssel': 'wrench',
            'ratsche': 'ratchet',
            
            # 清洁设备
            'reinigungsgerät': 'cleaning_equipment',
            'sauger': 'vacuum',
            'hochdruckreiniger': 'pressure_washer',
            'scheuersaugmaschine': 'scrubber_dryer',
            'kehrmaschine': 'sweeper',
            
            # 测量设备
            'messgerät': 'measuring_device',
            'multimeter': 'multimeter',
            'thermometer': 'thermometer',
            'manometer': 'pressure_gauge',
            'waage': 'scale',
            
            # 压缩机/泵类
            'kompressor': 'compressor',
            'pumpe': 'pump',
            'generator': 'generator',
            
            # 安全设备
            'helm': 'helmet',
            'handschuh': 'glove',
            'schutzbrille': 'safety_glasses',
            'atemschutz': 'respirator',
            
            # 存储/运输
            'koffer': 'case',
            'werkzeugkoffer': 'tool_case',
            'wagen': 'cart',
            'leiter': 'ladder',
            'regal': 'shelf'
        }
        
        # 应用场景关键词 (第二优先级)
        self.application_keywords = {
            'industrie': 'industrial',
            'profi': 'professional', 
            'handwerk': 'craft',
            'bau': 'construction',
            'garten': 'garden',
            'haushalt': 'household',
            'werkstatt': 'workshop',
            'büro': 'office'
        }
        
        # 特殊功能关键词 (第三优先级)
        self.feature_keywords = {
            'akku': 'battery',
            'elektrisch': 'electric',
            'hydraulisch': 'hydraulic', 
            'pneumatisch': 'pneumatic',
            'digital': 'digital',
            'automatisch': 'automatic',
            'kabellos': 'cordless',
            'tragbar': 'portable'
        }
        
        # 需要完全移除的无意义词汇
        self.remove_keywords = {
            'mm', 'cm', 'kg', 'din', 'iso', 'ce', 'gs',
            'edelstahl', 'stahl', 'kunststoff', 'aluminium', 'metall',
            'sie', 'die', 'der', 'das', 'und', 'mit', 'für', 'von', 'zu',
            'quot', 've:', 'stück', 'außen', 'innen', 'serie', 'typ',
            'bedienelemente', 'maschinenelemente', 'normalien', 'normelemente', 'normteile'
        }
        
        # 品牌关键词 (仅在特别重要时保留)
        self.brand_keywords = {
            'kärcher', 'bosch', 'makita', 'dewalt', 'milwaukee', 'festool',
            'rems', 'hymer', 'güde', 'bahco', 'ganter'
        }
        
    def extract_meaningful_tags_from_name(self, product_name):
        """从产品名称中提取有意义的Tags"""
        name_lower = str(product_name).lower()
        words = re.findall(r'\b\w+\b', name_lower)
        
        extracted_tags = []
        
        # 1. 寻找产品功能关键词
        for word in words:
            for func_keyword in self.function_keywords:
                if func_keyword in word or word in func_keyword:
                    extracted_tags.append(func_keyword.title())
                    break
        
        # 2. 寻找特殊功能关键词
        for word in words:
            for feature_keyword in self.feature_keywords:
                if feature_keyword in word or word in feature_keyword:
                    extracted_tags.append(feature_keyword.title())
                    break
        
        # 3. 寻找应用场景关键词
        for word in words:
            for app_keyword in self.application_keywords:
                if app_keyword in word or word in app_keyword:
                    extracted_tags.append(app_keyword.title())
                    break
        
        return list(set(extracted_tags))  # 去重
        
    def optimize_tags_improved(self, tags_str, product_name, category=''):
        """改进版Tags优化"""
        if pd.isna(tags_str) or tags_str == '':
            # 如果没有原始Tags，从产品名称提取
            return ', '.join(self.extract_meaningful_tags_from_name(product_name)[:2])
        
        # 分割原始Tags
        if ',' in str(tags_str):
            original_tags = [tag.strip() for tag in str(tags_str).split(',')]
        else:
            original_tags = [str(tags_str).strip()]
        
        # 分类Tags
        function_tags = []
        feature_tags = []
        application_tags = []
        brand_tags = []
        
        for tag in original_tags:
            tag_lower = tag.lower().strip()
            
            # 跳过无意义的Tags
            if (not tag_lower or len(tag_lower) < 2 or 
                any(remove_word in tag_lower for remove_word in self.remove_keywords)):
                continue
            
            # 分类有意义的Tags
            if any(func in tag_lower for func in self.function_keywords):
                function_tags.append(tag)
            elif any(feature in tag_lower for feature in self.feature_keywords):
                feature_tags.append(tag)
            elif any(app in tag_lower for app in self.application_keywords):
                application_tags.append(tag)
            elif any(brand in tag_lower for brand in self.brand_keywords):
                brand_tags.append(tag)
        
        # 按优先级选择最佳Tags
        final_tags = []
        
        # 第一优先级: 产品功能
        if function_tags:
            final_tags.append(function_tags[0])
        
        # 第二优先级: 特殊功能或应用场景
        if len(final_tags) < 2:
            if feature_tags:
                final_tags.append(feature_tags[0])
            elif application_tags:
                final_tags.append(application_tags[0])
        
        # 如果还没有足够的Tags，从产品名称提取
        if len(final_tags) < 2:
            name_tags = self.extract_meaningful_tags_from_name(product_name)
            for name_tag in name_tags:
                if name_tag not in final_tags and len(final_tags) < 2:
                    final_tags.append(name_tag)
        
        # 如果仍然没有Tags，保留最有意义的原始Tag
        if not final_tags:
            meaningful_original = [tag for tag in original_tags 
                                 if len(tag.strip()) > 3 and 
                                 not any(remove_word in tag.lower() for remove_word in self.remove_keywords)]
            if meaningful_original:
                final_tags.append(meaningful_original[0])
        
        return ', '.join(final_tags[:2])  # 最多2个Tags
        
    def optimize_file_improved(self, file_path):
        """改进版文件优化"""
        file_name = Path(file_path).name
        print(f"开始改进版优化: {file_name}")
        
        try:
            df = pd.read_csv(file_path, encoding='utf-8-sig')
            print(f"原始数据: {len(df):,} 行")
            
            tags_optimized = 0
            tag_quality_stats = {
                'function_tags': 0,
                'feature_tags': 0, 
                'application_tags': 0,
                'extracted_from_name': 0,
                'empty_tags': 0
            }
            
            for idx, row in df.iterrows():
                if 'Tags' in df.columns:
                    original_tags = row['Tags']
                    product_name = row['Name']
                    category = row.get('Categories', '')
                    
                    # 使用改进版优化
                    optimized_tags = self.optimize_tags_improved(original_tags, product_name, category)
                    df.at[idx, 'Tags'] = optimized_tags
                    
                    # 统计Tags质量
                    if optimized_tags:
                        tags_optimized += 1
                        
                        # 分析Tags类型
                        tag_list = [tag.strip().lower() for tag in optimized_tags.split(',')]
                        for tag in tag_list:
                            if any(func in tag for func in self.function_keywords):
                                tag_quality_stats['function_tags'] += 1
                            elif any(feature in tag for feature in self.feature_keywords):
                                tag_quality_stats['feature_tags'] += 1
                            elif any(app in tag for app in self.application_keywords):
                                tag_quality_stats['application_tags'] += 1
                    else:
                        tag_quality_stats['empty_tags'] += 1
                
                # 显示进度
                if (idx + 1) % 5000 == 0:
                    print(f"已处理 {idx + 1:,} 个产品...")
            
            print(f"Tags优化完成: {tags_optimized:,} 个产品")
            
            # 显示Tags质量统计
            print(f"\nTags质量统计:")
            for stat_type, count in tag_quality_stats.items():
                print(f"  {stat_type}: {count}")
            
            # 保存改进版文件
            output_file = file_path.replace('_optimized.csv', '_improved_tags.csv')
            if output_file == file_path:  # 如果没有_optimized后缀
                output_file = file_path.replace('.csv', '_improved_tags.csv')
            
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            print(f"改进版优化完成，保存到: {Path(output_file).name}")
            
            return output_file, tags_optimized, tag_quality_stats
            
        except Exception as e:
            print(f"优化失败: {e}")
            import traceback
            traceback.print_exc()
            return None, 0, {}
            
    def preview_improved_optimization(self, file_path, num_samples=5):
        """预览改进版优化效果"""
        print(f"预览改进版优化效果: {Path(file_path).name}")
        print("="*70)
        
        try:
            # 读取原始文件
            original_file = file_path.replace('_optimized.csv', '.csv')
            if not Path(original_file).exists():
                original_file = file_path.replace('_final.csv', '_final.csv')
            
            df_orig = pd.read_csv(original_file, encoding='utf-8-sig', nrows=20)
            df_current = pd.read_csv(file_path, encoding='utf-8-sig', nrows=20)
            
            for i in range(min(num_samples, len(df_orig))):
                row_orig = df_orig.iloc[i]
                row_current = df_current.iloc[i]
                
                print(f"示例 {i+1}:")
                print(f"产品名称: {str(row_orig['Name'])[:50]}...")
                print(f"原始Tags: {str(row_orig['Tags'])[:60]}...")
                print(f"当前Tags: {str(row_current['Tags'])}")
                
                # 生成改进版Tags
                improved_tags = self.optimize_tags_improved(
                    row_orig['Tags'], 
                    row_orig['Name'], 
                    row_orig.get('Categories', '')
                )
                print(f"改进Tags: {improved_tags}")
                
                # 分析改进效果
                if improved_tags:
                    tag_list = [tag.strip().lower() for tag in improved_tags.split(',')]
                    has_function = any(any(func in tag for func in self.function_keywords) for tag in tag_list)
                    has_feature = any(any(feature in tag for feature in self.feature_keywords) for tag in tag_list)
                    
                    if has_function:
                        print(f"✅ 包含产品功能关键词")
                    if has_feature:
                        print(f"✅ 包含特殊功能关键词")
                    if not has_function and not has_feature:
                        print(f"⚠️ 可能需要进一步优化")
                
                print("-" * 70)
                
        except Exception as e:
            print(f"预览失败: {e}")

def main():
    """主函数"""
    optimizer = ImprovedProfishopOptimizer()
    
    print("🚀 改进版Profishop Tags优化器")
    print("="*60)
    print("改进重点:")
    print("• 优先保留产品功能关键词 (bohrer, säge, schrauber等)")
    print("• 添加应用场景Tags (industrie, handwerk等)")
    print("• 移除技术参数Tags (mm, DIN, Edelstahl等)")
    print("• 从产品名称智能提取关键词")
    print("="*60)
    
    # 处理已优化的文件
    profishop_optimized_files = [
        'woocommerce_output_final/woocommerce_profishop-de_final_optimized.csv',
        'woocommerce_output_final/woocommerce_profishop-de-2_final_optimized.csv',
        'woocommerce_output_final/woocommerce_profishop-de-3_final_optimized.csv'
    ]
    
    total_improved = 0
    
    for i, file_path in enumerate(profishop_optimized_files, 1):
        if Path(file_path).exists():
            print(f"\n📋 预览 Profishop-{i} 改进效果:")
            optimizer.preview_improved_optimization(file_path, 3)
            
            print(f"\n🔧 开始改进 Profishop-{i} Tags:")
            output_file, improved_count, quality_stats = optimizer.optimize_file_improved(file_path)
            
            if output_file:
                print(f"✅ Profishop-{i} Tags改进成功!")
                total_improved += improved_count
            else:
                print(f"❌ Profishop-{i} Tags改进失败")
        else:
            print(f"❌ 文件不存在: {file_path}")
        
        print("\n" + "="*60)
    
    print(f"\n📊 改进版优化总结")
    print("="*60)
    print(f"总改进产品数: {total_improved:,}")
    print("改进效果:")
    print("✅ 保留真正的产品功能关键词")
    print("✅ 移除无意义的技术参数")
    print("✅ 提升Tags的SEO和用户价值")
    print("✅ 每个产品最多2个高质量Tags")

if __name__ == "__main__":
    main()
