#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为Obelink和Bauhaus生成短描述
"""

import pandas as pd
import re
from pathlib import Path

class ShortDescriptionGenerator:
    def __init__(self):
        """初始化短描述生成器"""
        
        # Obelink (户外露营用品) 卖点配置
        self.obelink_selling_points = {
            # 露营帐篷类
            'zelt': [
                '✓ Wetterfeste Konstruktion für alle Jahreszeiten',
                '✓ Einfacher Aufbau mit praktischem Zubehör',
                '✓ Hochwertige Materialien für Langlebigkeit'
            ],
            'vorzelt': [
                '✓ Perfekte Erweiterung für Ihren Wohnwagen',
                '✓ Robuste Konstruktion & einfache Montage',
                '✓ Zusätzlicher Wohnraum bei jedem Wetter'
            ],
            
            # 户外用品
            'outdoor': [
                '✓ Speziell für Outdoor-Aktivitäten entwickelt',
                '✓ Strapazierfähige Materialien & kompaktes Design',
                '✓ Zuverlässiger Begleiter für jedes Abenteuer'
            ],
            'camping': [
                '✓ Unverzichtbar für jeden Campingausflug',
                '✓ Praktische Funktionen & platzsparende Lösung',
                '✓ Hochwertige Qualität für den Dauereinsatz'
            ],
            
            # 厨房用品
            'kochen': [
                '✓ Praktische Küchenhelfer für unterwegs',
                '✓ Leichte Reinigung & kompakte Aufbewahrung',
                '✓ Funktionales Design für den täglichen Gebrauch'
            ],
            'grill': [
                '✓ Perfekt für gesellige Grillabende',
                '✓ Einfache Handhabung & gleichmäßige Hitze',
                '✓ Robuste Bauweise für den Outdoor-Einsatz'
            ],
            
            # 睡眠用品
            'schlafen': [
                '✓ Erholsamer Schlaf auch unterwegs',
                '✓ Komfortable Materialien & optimale Wärmeisolierung',
                '✓ Kompakt verstaubar für jede Reise'
            ],
            'schlafsack': [
                '✓ Optimaler Schlafkomfort bei jeder Temperatur',
                '✓ Leichtes Gewicht & kleines Packmaß',
                '✓ Strapazierfähige Materialien für Langlebigkeit'
            ],
            
            # 花园用品
            'garten': [
                '✓ Verschönert jeden Garten & Außenbereich',
                '✓ Wetterfeste Materialien für ganzjährigen Einsatz',
                '✓ Stilvolles Design trifft praktische Funktionalität'
            ],
            'kissen': [
                '✓ Maximaler Sitzkomfort für entspannte Stunden',
                '✓ Pflegeleichte Materialien & ansprechendes Design',
                '✓ Wetterfest & UV-beständig für den Außenbereich'
            ],
            
            # 通用
            'generic': [
                '✓ Hochwertige Qualität für den täglichen Gebrauch',
                '✓ Praktisches Design & zuverlässige Funktionalität',
                '✓ Optimales Preis-Leistungs-Verhältnis'
            ]
        }
        
        # Bauhaus (建材家居) 卖点配置
        self.bauhaus_selling_points = {
            # 浴室设备
            'bad': [
                '✓ Moderne Badausstattung für höchsten Komfort',
                '✓ Hochwertige Materialien & langlebige Verarbeitung',
                '✓ Stilvolles Design für jedes Badezimmer'
            ],
            'dusche': [
                '✓ Luxuriöses Duscherlebnis mit optimaler Funktionalität',
                '✓ Einfache Installation & pflegeleichte Oberflächen',
                '✓ Moderne Technik für täglichen Komfort'
            ],
            
            # 加热设备
            'heizen': [
                '✓ Effiziente Wärmelösung für behagliche Räume',
                '✓ Energiesparende Technologie & einfache Bedienung',
                '✓ Zuverlässige Heizleistung für jede Raumgröße'
            ],
            'infrarot': [
                '✓ Moderne Infrarot-Heiztechnik für angenehme Wärme',
                '✓ Energieeffizient & gleichmäßige Wärmeverteilung',
                '✓ Stilvolles Design als dekorativer Raumschmuck'
            ],
            
            # 花园建材
            'garten': [
                '✓ Hochwertige Gartenlösungen für jeden Außenbereich',
                '✓ Wetterfeste Materialien & dauerhafte Konstruktion',
                '✓ Funktional & ästhetisch ansprechend gestaltet'
            ],
            'gabionen': [
                '✓ Stabile Gabionenlösung für Garten & Landschaftsbau',
                '✓ Langlebige Konstruktion & vielseitige Einsatzmöglichkeiten',
                '✓ Einfache Montage mit professionellem Ergebnis'
            ],
            
            # 装饰材料
            'tapeten': [
                '✓ Hochwertige Wandgestaltung für individuelle Räume',
                '✓ Einfache Verarbeitung & langanhaltende Farbbrillanz',
                '✓ Vielfältige Designs für jeden Wohnstil'
            ],
            'fototapete': [
                '✓ Beeindruckende Wandgestaltung mit fotorealistischen Motiven',
                '✓ Hochauflösende Druckqualität & einfache Anbringung',
                '✓ Verwandelt jeden Raum in ein Kunstwerk'
            ],
            
            # 工具设备
            'werkzeug': [
                '✓ Professionelle Werkzeuge für Handwerk & Heimwerken',
                '✓ Robuste Bauweise & präzise Funktionalität',
                '✓ Zuverlässige Qualität für anspruchsvolle Arbeiten'
            ],
            'maschinen': [
                '✓ Leistungsstarke Maschinen für effizientes Arbeiten',
                '✓ Hochwertige Technik & benutzerfreundliche Bedienung',
                '✓ Professionelle Ergebnisse für Handwerk & Industrie'
            ],
            
            # 通用
            'generic': [
                '✓ Hochwertige Bauhaus-Qualität für Ihr Zuhause',
                '✓ Funktionales Design & zuverlässige Verarbeitung',
                '✓ Optimale Lösung für anspruchsvolle Projekte'
            ]
        }
        
    def detect_product_category_obelink(self, name, category):
        """检测Obelink产品类别"""
        text = (str(name) + ' ' + str(category)).lower()
        
        # 按优先级检测
        if any(word in text for word in ['zelt', 'tent']):
            return 'zelt'
        elif any(word in text for word in ['vorzelt', 'awning']):
            return 'vorzelt'
        elif any(word in text for word in ['schlafsack', 'sleeping']):
            return 'schlafsack'
        elif any(word in text for word in ['schlafen', 'sleep', 'decke']):
            return 'schlafen'
        elif any(word in text for word in ['grill', 'bbq']):
            return 'grill'
        elif any(word in text for word in ['kochen', 'küche', 'cook']):
            return 'kochen'
        elif any(word in text for word in ['kissen', 'cushion', 'pillow']):
            return 'kissen'
        elif any(word in text for word in ['garten', 'garden']):
            return 'garten'
        elif any(word in text for word in ['outdoor', 'camping']):
            return 'outdoor'
        else:
            return 'generic'
            
    def detect_product_category_bauhaus(self, name, category):
        """检测Bauhaus产品类别"""
        text = (str(name) + ' ' + str(category)).lower()
        
        # 按优先级检测
        if any(word in text for word in ['dusche', 'shower', 'eckdusche']):
            return 'dusche'
        elif any(word in text for word in ['bad', 'sanitär', 'bathroom']):
            return 'bad'
        elif any(word in text for word in ['infrarot', 'infrared']):
            return 'infrarot'
        elif any(word in text for word in ['heizen', 'heizung', 'heating']):
            return 'heizen'
        elif any(word in text for word in ['fototapete', 'photo']):
            return 'fototapete'
        elif any(word in text for word in ['tapete', 'wallpaper']):
            return 'tapeten'
        elif any(word in text for word in ['gabionen', 'gabion']):
            return 'gabionen'
        elif any(word in text for word in ['garten', 'garden', 'freizeit']):
            return 'garten'
        elif any(word in text for word in ['maschine', 'machine', 'motor']):
            return 'maschinen'
        elif any(word in text for word in ['werkzeug', 'tool']):
            return 'werkzeug'
        else:
            return 'generic'
            
    def generate_short_description(self, name, category, store_type):
        """生成短描述"""
        if pd.isna(name) or name == '':
            return ''
        
        # 清理产品名称 (移除过长的技术参数)
        clean_name = str(name)
        if len(clean_name) > 60:
            # 保留前60个字符，在合适的位置截断
            clean_name = clean_name[:60]
            if ' ' in clean_name:
                clean_name = clean_name.rsplit(' ', 1)[0]
        
        # 根据商店类型选择卖点
        if store_type == 'obelink':
            category_detected = self.detect_product_category_obelink(name, category)
            selling_points = self.obelink_selling_points.get(category_detected, 
                           self.obelink_selling_points['generic'])
        else:  # bauhaus
            category_detected = self.detect_product_category_bauhaus(name, category)
            selling_points = self.bauhaus_selling_points.get(category_detected,
                           self.bauhaus_selling_points['generic'])
        
        # 组合短描述
        short_desc = clean_name + ' ' + ' '.join(selling_points)
        
        return short_desc
        
    def generate_for_file(self, file_path, store_type):
        """为文件生成短描述"""
        file_name = Path(file_path).name
        print(f"开始为 {file_name} 生成短描述")
        
        try:
            df = pd.read_csv(file_path, encoding='utf-8-sig')
            print(f"原始数据: {len(df):,} 行")
            
            generated_count = 0
            category_stats = {}
            
            for idx, row in df.iterrows():
                name = row['Name']
                category = row.get('Categories', '')
                
                # 生成短描述
                short_desc = self.generate_short_description(name, category, store_type)
                df.at[idx, 'Short description'] = short_desc
                
                if short_desc:
                    generated_count += 1
                    
                    # 统计类别
                    if store_type == 'obelink':
                        detected_cat = self.detect_product_category_obelink(name, category)
                    else:
                        detected_cat = self.detect_product_category_bauhaus(name, category)
                    
                    category_stats[detected_cat] = category_stats.get(detected_cat, 0) + 1
                
                # 显示进度
                if (idx + 1) % 5000 == 0:
                    print(f"已处理 {idx + 1:,} 个产品...")
            
            print(f"短描述生成完成: {generated_count:,} 个产品")
            
            # 显示类别统计
            print(f"\n产品类别分布:")
            for category, count in sorted(category_stats.items(), key=lambda x: x[1], reverse=True):
                percentage = count / generated_count * 100 if generated_count > 0 else 0
                print(f"  {category}: {count:,} 个 ({percentage:.1f}%)")
            
            # 保存文件
            output_file = file_path.replace('.csv', '_with_short_desc.csv')
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            print(f"文件保存到: {Path(output_file).name}")
            
            return output_file, generated_count, category_stats
            
        except Exception as e:
            print(f"生成失败: {e}")
            import traceback
            traceback.print_exc()
            return None, 0, {}
            
    def preview_generation(self, file_path, store_type, num_samples=5):
        """预览生成效果"""
        print(f"预览 {store_type.title()} 短描述生成效果:")
        print("="*60)
        
        try:
            df = pd.read_csv(file_path, encoding='utf-8-sig', nrows=20)
            
            for i in range(min(num_samples, len(df))):
                row = df.iloc[i]
                
                name = row['Name']
                category = row.get('Categories', '')
                
                print(f"示例 {i+1}:")
                print(f"产品名称: {str(name)[:50]}...")
                print(f"产品分类: {str(category)[:40]}...")
                
                # 生成短描述
                short_desc = self.generate_short_description(name, category, store_type)
                print(f"生成短描述: {short_desc[:80]}...")
                
                # 检测类别
                if store_type == 'obelink':
                    detected_cat = self.detect_product_category_obelink(name, category)
                else:
                    detected_cat = self.detect_product_category_bauhaus(name, category)
                
                print(f"检测类别: {detected_cat}")
                print("-" * 60)
                
        except Exception as e:
            print(f"预览失败: {e}")

def main():
    """主函数"""
    generator = ShortDescriptionGenerator()
    
    print("🚀 Obelink & Bauhaus 短描述生成器")
    print("="*60)
    print("功能:")
    print("• 为没有短描述的产品生成定制化短描述")
    print("• 基于产品名称和分类智能识别产品类型")
    print("• 每个产品3个专业卖点")
    print("• 德语表达自然流畅")
    print("="*60)
    
    files_to_process = [
        ('woocommerce_output_final/woocommerce_obelink-de_final.csv', 'obelink'),
        ('woocommerce_output_final/woocommerce_bauhaus-at-de-图片前两图_final.csv', 'bauhaus')
    ]
    
    total_generated = 0
    
    for file_path, store_type in files_to_process:
        if Path(file_path).exists():
            print(f"\n📋 预览 {store_type.title()} 生成效果:")
            generator.preview_generation(file_path, store_type, 3)
            
            print(f"\n🔧 开始为 {store_type.title()} 生成短描述:")
            output_file, generated_count, category_stats = generator.generate_for_file(file_path, store_type)
            
            if output_file:
                print(f"✅ {store_type.title()} 短描述生成成功!")
                total_generated += generated_count
            else:
                print(f"❌ {store_type.title()} 短描述生成失败")
        else:
            print(f"❌ 文件不存在: {file_path}")
        
        print("\n" + "="*60)
    
    print(f"\n📊 总体生成统计")
    print("="*60)
    print(f"总生成短描述数: {total_generated:,}")
    print("生成效果:")
    print("✅ 基于产品名称和分类智能生成")
    print("✅ 每个产品3个专业卖点")
    print("✅ 德语表达自然流畅")
    print("✅ 针对不同产品类型定制化")

if __name__ == "__main__":
    main()
