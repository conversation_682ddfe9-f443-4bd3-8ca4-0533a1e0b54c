#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终修复版德国数据源WooCommerce转换脚本
修复HTML、图片、分类和短描述问题
"""

import pandas as pd
import numpy as np
from pathlib import Path
import re
import sys
import html
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

class FinalFixedConverter:
    def __init__(self):
        """初始化最终修复版转换器"""
        self.setup_logging()
        
        # 德语字段映射
        self.field_mappings = {
            'name_fields': ['title', 'post_title', 'Name', 'product_name', 'titel'],
            'price_fields': ['price', 'regular_price', 'price/100', 'cost', 'preis'],
            'description_fields': ['description', 'detail', 'post_content', 'desc', 'beschreibung'],
            'short_desc_fields': ['short description', 'post_excerpt', 'short_description', 'kurzbeschreibung'],
            'category_fields': ['category', 'cate', 'tax:product_cat', 'Categories', 'kategorie'],
            'image_fields': ['image', 'images', 'image0', 'featured', 'photo', 'bild', 'Images'],
            'sku_fields': ['sku', 'SKU', 'ID', 'product_id', 'artikelnummer'],
            'brand_fields': ['Brand', 'attribute:Brand', 'brand', 'manufacturer', 'Marke', 'Hersteller'],
            'mpn_fields': ['mpn', 'MPN', 'MFG', 'model', 'artikelnummer'],
            'upc_fields': ['UPC', 'upc', 'barcode', 'ean', 'EAN', 'GTIN'],
            'tags_fields': ['tags', 'schlagwörter', 'keywords', 'stichwörter', 'Tags', 'tag']
        }
        
        # WooCommerce默认值
        self.wc_defaults = {
            'Type': 'simple',
            'Published': 1,
            'Is featured?': 0,
            'Visibility in catalog': 'visible',
            'In stock?': 1,
            'Stock': '',
            'Backorders allowed?': 0,
            'Sold individually?': 0,
            'Allow customer reviews?': 1,
            'Tax status': 'taxable',
            'Tax class': '',
            'Low stock amount': '',
            'Weight (kg)': '',
            'Length (cm)': '',
            'Width (cm)': '',
            'Height (cm)': '',
            'Purchase note': '',
            'Shipping class': '',
            'Date sale price starts': '',
            'Date sale price ends': '',
            'Download limit': '',
            'Download expiry days': '',
            'Parent': '',
            'Grouped products': '',
            'Upsells': '',
            'Cross-sells': '',
            'External URL': '',
            'Button text': '',
            'Position': 0
        }
        
        # WooCommerce友好的HTML标签转换规则
        self.wc_html_replacements = {
            # 标题标签 - 统一转换为h3
            r'<h1[^>]*>': '<h3>',
            r'</h1>': '</h3>',
            r'<h2[^>]*>': '<h3>',
            r'</h2>': '</h3>',
            r'<h4[^>]*>': '<h3>',
            r'</h4>': '</h3>',
            r'<h5[^>]*>': '<h3>',
            r'</h5>': '</h3>',
            r'<h6[^>]*>': '<h3>',
            r'</h6>': '</h3>',
            
            # 复杂标签转换为简单标签
            r'<section[^>]*>': '<div>',
            r'</section>': '</div>',
            r'<dl[^>]*>': '<ul>',
            r'</dl>': '</ul>',
            r'<dt[^>]*>': '<li><strong>',
            r'</dt>': '</strong>',
            r'<dd[^>]*>': ': ',
            r'</dd>': '</li>',
            
            # 表格保持但简化
            r'<table[^>]*>': '<table>',
            r'<tbody[^>]*>': '',
            r'</tbody>': '',
            r'<tr[^>]*>': '<tr>',
            r'<th[^>]*>': '<th>',
            r'<td[^>]*>': '<td>',
            
            # 字体和样式标签
            r'<font[^>]*>': '<span>',
            r'</font>': '</span>',
            r'<center[^>]*>': '<div style="text-align: center;">',
            r'</center>': '</div>',
            r'<b[^>]*>': '<strong>',
            r'</b>': '</strong>',
            r'<i[^>]*>': '<em>',
            r'</i>': '</em>',
            
            # 清理特殊属性
            r'<span[^>]*data-ui-id="[^"]*"[^>]*>': '<span>',
            r'<span[^>]*class="[^"]*"[^>]*>': '<span>',
        }
        
        # 危险标签列表
        self.dangerous_tags = ['script', 'style', 'iframe', 'noscript', 'object', 'embed']
        
        # 图片分隔符 - 修复版
        self.image_separators = ['|||', '|', ';', '\n', ',']  # 逗号放最后，避免误分割URL参数
        
    def setup_logging(self):
        """设置日志记录"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('final_fixed_conversion.log', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def detect_field(self, df: pd.DataFrame, field_list: List[str]) -> Optional[str]:
        """智能字段检测"""
        for field in field_list:
            for col in df.columns:
                if field.lower() == col.lower():
                    return col
        for field in field_list:
            for col in df.columns:
                if field.lower() in col.lower():
                    return col
        return None
        
    def clean_german_price(self, price_value: Any) -> float:
        """德语价格格式清理"""
        if pd.isna(price_value) or price_value == '':
            return 0.0
            
        price_str = str(price_value).strip()
        price_str = re.sub(r'[€$£¥\s]', '', price_str)
        
        if ',' in price_str and '.' in price_str:
            price_str = price_str.replace('.', '').replace(',', '.')
        elif ',' in price_str:
            parts = price_str.split(',')
            if len(parts) == 2 and len(parts[1]) <= 2:
                price_str = price_str.replace(',', '.')
            else:
                price_str = price_str.replace(',', '')
                
        try:
            return float(price_str)
        except ValueError:
            return 0.0
            
    def clean_woocommerce_html(self, html_content: Any) -> str:
        """WooCommerce友好的HTML清理"""
        if pd.isna(html_content) or html_content == '':
            return ''
            
        content = str(html_content)
        
        # 1. 移除危险标签
        for tag in self.dangerous_tags:
            pattern = f'<{tag}[^>]*>.*?</{tag}>'
            content = re.sub(pattern, '', content, flags=re.DOTALL | re.IGNORECASE)
            
        # 2. WooCommerce友好的标签转换
        for pattern, replacement in self.wc_html_replacements.items():
            content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
            
        # 3. 移除危险事件处理器
        event_patterns = [
            r'\s+on(click|load|mouse\w+|key\w+|focus|blur|change|submit)\s*=\s*["\'][^"\']*["\']',
            r'\s+on(click|load|mouse\w+|key\w+|focus|blur|change|submit)\s*=\s*[^>\s]+',
        ]
        for pattern in event_patterns:
            content = re.sub(pattern, '', content, flags=re.IGNORECASE)
            
        # 4. 清理多余空白但保留德语字符
        content = re.sub(r'\s+', ' ', content).strip()
        
        # 5. HTML实体解码
        content = html.unescape(content)
        
        return content
        
    def process_categories(self, category_str: Any) -> str:
        """修复版分类处理 - 正确处理逗号"""
        if pd.isna(category_str) or category_str == '':
            return 'General'
            
        category = str(category_str).strip()
        
        # 修复：不要将逗号替换为>，逗号表示"或"的关系
        # 可以用 & 或 / 替换逗号，或者保持原样
        if ',' in category and ' > ' not in category:
            # 如果只有逗号没有>，可能是并列关系，用 & 替换
            category = category.replace(',', ' & ')
        
        # 处理其他分隔符
        category = category.replace('|||', ' > ')
        
        # 分割并清理各级分类
        parts = [part.strip() for part in category.split(' > ') if part.strip()]
        
        # 去重并限制层级数量（最多4级）
        unique_parts = []
        for part in parts[:4]:
            if part not in unique_parts:
                unique_parts.append(part)
                
        return ' > '.join(unique_parts) if unique_parts else 'General'
        
    def process_images(self, image_str: Any) -> str:
        """修复版图片处理 - 正确提取前5张"""
        if pd.isna(image_str) or image_str == '':
            return ''
            
        image_content = str(image_str).strip()
        
        # 修复：更智能的图片URL分割
        urls = []
        
        # 首先尝试明确的分隔符
        for separator in ['|||', '|', ';', '\n']:
            if separator in image_content:
                urls = [url.strip() for url in image_content.split(separator)]
                break
        else:
            # 如果没有明确分隔符，尝试逗号（但要小心URL参数）
            if ',' in image_content:
                # 检查是否是URL参数中的逗号
                potential_urls = image_content.split(',')
                urls = []
                current_url = ""
                
                for part in potential_urls:
                    part = part.strip()
                    if current_url:
                        # 检查是否是URL的延续
                        if ('http' not in part and '.' not in part) or ('=' in part and 'http' not in part):
                            current_url += ',' + part
                        else:
                            urls.append(current_url)
                            current_url = part
                    else:
                        current_url = part
                
                if current_url:
                    urls.append(current_url)
            else:
                urls = [image_content]
        
        # 清理和验证URL，严格限制前5张
        valid_urls = []
        for url in urls:
            if len(valid_urls) >= 5:  # 修复：严格限制前5张
                break
                
            url = url.strip()
            if url and ('http' in url or url.startswith('//')):
                if url.startswith('//'):
                    url = 'https:' + url
                if url not in valid_urls:  # 去重
                    valid_urls.append(url)
                    
        return ','.join(valid_urls)
        
    def generate_sku(self, name: str, brand: str = "", original_id: str = "", prefix: str = "DE") -> str:
        """SKU生成逻辑"""
        if original_id:
            return f"{prefix}-{original_id}"
            
        sku_parts = []
        
        if brand:
            brand_clean = re.sub(r'[^A-Za-z0-9]', '', str(brand))[:8].upper()
            sku_parts.append(brand_clean)
            
        if name:
            name_clean = re.sub(r'[^A-Za-z0-9]', '', str(name))[:8].upper()
            sku_parts.append(name_clean)
            
        if not sku_parts:
            return f"{prefix}-PRODUCT"
            
        sku = '-'.join(sku_parts)
        return sku[:20]
        
    def generate_description(self, row: pd.Series, field_map: Dict[str, str]) -> tuple:
        """修复版描述生成逻辑 - 短描述为空时不生成"""
        long_desc = ""
        short_desc = ""
        
        # 1. 处理长描述
        long_desc_fields = ['description', 'detail', 'post_content', 'Description']
        for field_key in long_desc_fields:
            if field_key in field_map and field_map[field_key]:
                col_name = field_map[field_key]
                if pd.notna(row[col_name]):
                    desc = self.clean_woocommerce_html(row[col_name])
                    if len(desc.strip()) > 10:
                        long_desc = desc
                        break
                        
        # 2. 处理短描述 - 修复：源文件为空时不生成
        short_desc_fields = ['short_desc', 'short_description', 'post_excerpt']
        for field_key in short_desc_fields:
            if field_key in field_map and field_map[field_key]:
                col_name = field_map[field_key]
                if pd.notna(row[col_name]):
                    desc = self.clean_woocommerce_html(row[col_name])
                    if len(desc.strip()) > 5:
                        short_desc = desc
                        break
        
        # 修复：如果源文件短描述为空，不要生成短描述
        # 不再基于产品名生成短描述
        
        # 3. 如果没有长描述，使用短描述包装在<p>标签中
        if not long_desc and short_desc:
            long_desc = f"<p>{short_desc}</p>"
            
        return long_desc, short_desc

    def create_woocommerce_product(self, row: pd.Series, row_id: int, field_map: Dict[str, str]) -> Dict[str, Any]:
        """最终修复版WooCommerce产品数据创建"""
        wc_product = {}

        # ID列留空
        wc_product['ID'] = ''
        wc_product['Type'] = self.wc_defaults['Type']

        # 产品名称
        name = ""
        if field_map.get('name') and pd.notna(row[field_map['name']]):
            name = str(row[field_map['name']]).strip()
        wc_product['Name'] = name if name else f"Product {row_id}"

        # 品牌信息
        brand = ""
        if field_map.get('brand') and pd.notna(row[field_map['brand']]):
            brand = str(row[field_map['brand']]).strip()

        # SKU生成
        original_id = ""
        if field_map.get('sku') and pd.notna(row[field_map['sku']]):
            original_id = str(row[field_map['sku']]).strip()
        wc_product['SKU'] = self.generate_sku(name, brand, original_id)

        # 价格处理 - 使用原始价格
        regular_price = 0.0
        if field_map.get('price') and pd.notna(row[field_map['price']]):
            regular_price = self.clean_german_price(row[field_map['price']])

        wc_product['Regular price'] = regular_price if regular_price > 0 else ''

        # 促销价格处理
        sale_price = ""
        if field_map.get('sale_price') and pd.notna(row[field_map['sale_price']]):
            original_sale = self.clean_german_price(row[field_map['sale_price']])
            if original_sale > 0 and (regular_price == 0 or original_sale < regular_price):
                sale_price = original_sale

        wc_product['Sale price'] = sale_price

        # 修复版描述处理
        long_desc, short_desc = self.generate_description(row, field_map)
        wc_product['Description'] = long_desc
        wc_product['Short description'] = short_desc

        # 修复版分类处理
        category = 'General'
        if field_map.get('category') and pd.notna(row[field_map['category']]):
            category = self.process_categories(row[field_map['category']])
        wc_product['Categories'] = category

        # 修复版图片处理
        images = ""
        if field_map.get('image') and pd.notna(row[field_map['image']]):
            images = self.process_images(row[field_map['image']])
        wc_product['Images'] = images

        # 标签处理
        tags = ""
        if field_map.get('tags') and pd.notna(row[field_map['tags']]):
            tags_str = str(row[field_map['tags']]).strip()
            tags = tags_str.replace(',', ', ').replace(';', ', ')
        elif brand:
            tags = brand
        wc_product['Tags'] = tags

        # 修复版属性处理
        attr_count = 1

        # 品牌属性
        if brand:
            wc_product['Attribute 1 name'] = 'Brand'
            wc_product['Attribute 1 value(s)'] = brand
            wc_product['Attribute 1 visible'] = 1
            wc_product['Attribute 1 global'] = 0
            attr_count += 1
        else:
            wc_product['Attribute 1 name'] = ''
            wc_product['Attribute 1 value(s)'] = ''

        # MPN属性
        mpn = ""
        if field_map.get('mpn') and pd.notna(row[field_map['mpn']]):
            mpn = str(row[field_map['mpn']]).strip()

        if mpn:
            wc_product[f'Attribute {attr_count} name'] = 'MPN'
            wc_product[f'Attribute {attr_count} value(s)'] = mpn
            wc_product[f'Attribute {attr_count} visible'] = 1
            wc_product[f'Attribute {attr_count} global'] = 0
            attr_count += 1
        else:
            wc_product[f'Attribute {attr_count} name'] = ''
            wc_product[f'Attribute {attr_count} value(s)'] = ''

        # UPC/EAN属性
        upc = ""
        if field_map.get('upc') and pd.notna(row[field_map['upc']]):
            upc = str(row[field_map['upc']]).strip()

        if upc:
            wc_product[f'Attribute {attr_count} name'] = 'UPC'
            wc_product[f'Attribute {attr_count} value(s)'] = upc
            wc_product[f'Attribute {attr_count} visible'] = 1
            wc_product[f'Attribute {attr_count} global'] = 0
            attr_count += 1
        else:
            wc_product[f'Attribute {attr_count} name'] = ''
            wc_product[f'Attribute {attr_count} value(s)'] = ''

        # 确保所有属性列都存在（最多4个属性）
        for i in range(1, 5):
            for suffix in ['name', 'value(s)', 'visible', 'global']:
                key = f'Attribute {i} {suffix}'
                if key not in wc_product:
                    if suffix in ['visible', 'global']:
                        wc_product[key] = 0
                    else:
                        wc_product[key] = ''

        # 应用所有默认值
        for key, value in self.wc_defaults.items():
            if key not in wc_product:
                wc_product[key] = value

        return wc_product

    def convert_file(self, input_file: str, output_file: str = None) -> Optional[str]:
        """转换单个文件"""
        try:
            self.logger.info(f"开始处理文件: {Path(input_file).name}")

            # 读取数据
            if str(input_file).endswith('.csv'):
                df = pd.read_csv(input_file, encoding='utf-8-sig')
            else:
                df = pd.read_excel(input_file)

            self.logger.info(f"原始数据: {len(df)} 行, {len(df.columns)} 列")

            if len(df) == 0:
                self.logger.warning("文件为空")
                return None

            # 智能字段检测
            field_map = {}
            for field_type, field_list in self.field_mappings.items():
                detected_field = self.detect_field(df, field_list)
                field_map[field_type.replace('_fields', '')] = detected_field

            # 记录检测结果
            self.logger.info("字段检测结果:")
            for field_type, field_name in field_map.items():
                if field_name:
                    self.logger.info(f"  {field_type}: {field_name}")

            # 转换数据
            wc_products = []
            total_rows = len(df)

            for index, row in df.iterrows():
                try:
                    wc_product = self.create_woocommerce_product(row, index + 1, field_map)
                    wc_products.append(wc_product)

                    if (index + 1) % 1000 == 0:
                        self.logger.info(f"已处理 {index + 1}/{total_rows} 行...")

                except Exception as e:
                    self.logger.error(f"处理第 {index + 1} 行时出错: {e}")
                    continue

            # 创建DataFrame
            wc_df = pd.DataFrame(wc_products)

            # 确保列顺序正确
            wc_columns = [
                'ID', 'Type', 'SKU', 'Name', 'Published', 'Is featured?', 'Visibility in catalog',
                'Short description', 'Description', 'Date sale price starts', 'Date sale price ends',
                'Tax status', 'Tax class', 'In stock?', 'Stock', 'Low stock amount', 'Backorders allowed?',
                'Sold individually?', 'Weight (kg)', 'Length (cm)', 'Width (cm)', 'Height (cm)',
                'Allow customer reviews?', 'Purchase note', 'Sale price', 'Regular price', 'Categories',
                'Tags', 'Shipping class', 'Images', 'Download limit', 'Download expiry days', 'Parent',
                'Grouped products', 'Upsells', 'Cross-sells', 'External URL', 'Button text', 'Position',
                'Attribute 1 name', 'Attribute 1 value(s)', 'Attribute 1 visible', 'Attribute 1 global',
                'Attribute 2 name', 'Attribute 2 value(s)', 'Attribute 2 visible', 'Attribute 2 global',
                'Attribute 3 name', 'Attribute 3 value(s)', 'Attribute 3 visible', 'Attribute 3 global',
                'Attribute 4 name', 'Attribute 4 value(s)', 'Attribute 4 visible', 'Attribute 4 global'
            ]

            # 确保所有列都存在
            for col in wc_columns:
                if col not in wc_df.columns:
                    wc_df[col] = ''

            wc_df = wc_df[wc_columns]

            # 修复：确保ID和Stock列为空字符串而不是NaN
            wc_df['ID'] = ''
            wc_df['Stock'] = ''

            # 保存文件
            if output_file is None:
                input_path = Path(input_file)
                output_file = f"woocommerce_{input_path.stem}_final.csv"

            wc_df.to_csv(output_file, index=False, encoding='utf-8-sig')

            self.logger.info(f"转换完成: {output_file}")
            self.logger.info(f"产品数量: {len(wc_df)}")
            self.logger.info(f"数据完整性: {len(wc_df)}/{total_rows} = {len(wc_df)/total_rows*100:.1f}%")

            return output_file

        except Exception as e:
            self.logger.error(f"转换失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def batch_convert(self, input_dir: str, output_dir: str = None) -> List[str]:
        """批量转换目录中的所有文件"""
        input_path = Path(input_dir)

        if not input_path.exists():
            self.logger.error(f"输入目录不存在: {input_dir}")
            return []

        if output_dir is None:
            output_dir = "woocommerce_output_final"

        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        # 查找文件，排除临时文件
        files = []
        for pattern in ["*.xlsx", "*.xls", "*.csv"]:
            for file_path in input_path.glob(pattern):
                if not file_path.name.startswith('~$'):  # 排除Excel临时文件
                    files.append(file_path)

        if not files:
            self.logger.error("目录中没有找到数据文件")
            return []

        self.logger.info(f"开始批量转换")
        self.logger.info(f"输入目录: {input_dir}")
        self.logger.info(f"输出目录: {output_dir}")
        self.logger.info(f"文件数量: {len(files)}")

        converted_files = []
        total_products = 0
        start_time = datetime.now()

        for i, file_path in enumerate(files, 1):
            self.logger.info(f"\n[{i}/{len(files)}] " + "="*50)

            output_file = output_path / f"woocommerce_{file_path.stem}_final.csv"

            result = self.convert_file(str(file_path), str(output_file))

            if result:
                converted_files.append(result)
                try:
                    df = pd.read_csv(result, encoding='utf-8-sig')
                    total_products += len(df)
                except:
                    pass

        end_time = datetime.now()
        duration = end_time - start_time

        self.logger.info(f"\n🎉 批量转换完成!")
        self.logger.info(f"成功转换: {len(converted_files)}/{len(files)} 个文件")
        self.logger.info(f"总产品数: {total_products:,}")
        self.logger.info(f"处理时间: {duration}")
        if duration.total_seconds() > 0:
            self.logger.info(f"平均速度: {total_products/duration.total_seconds():.1f} 产品/秒")

        return converted_files

def main():
    """主函数"""
    converter = FinalFixedConverter()

    print("🇩🇪 最终修复版德国数据源WooCommerce转换工具")
    print("修复HTML、图片、分类和短描述问题")
    print("="*60)

    if len(sys.argv) > 1:
        input_path = sys.argv[1]

        if Path(input_path).is_file():
            # 单文件转换
            result = converter.convert_file(input_path)
            if result:
                print(f"✅ 转换成功: {result}")
            else:
                print("❌ 转换失败")

        elif Path(input_path).is_dir():
            # 批量转换
            output_dir = sys.argv[2] if len(sys.argv) > 2 else None
            converted_files = converter.batch_convert(input_path, output_dir)

            if converted_files:
                print(f"✅ 批量转换完成，共转换 {len(converted_files)} 个文件")
            else:
                print("❌ 批量转换失败")
        else:
            print(f"❌ 路径不存在: {input_path}")
    else:
        # 默认处理de目录
        de_dir = "源数据文件/de"
        if Path(de_dir).exists():
            converted_files = converter.batch_convert(de_dir)
            if converted_files:
                print("✅ 默认批量转换完成")
        else:
            print(f"❌ 默认目录不存在: {de_dir}")
            print("用法: python final_fixed_converter.py <输入文件/目录> [输出目录]")

if __name__ == "__main__":
    main()
