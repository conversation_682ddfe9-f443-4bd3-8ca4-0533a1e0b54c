#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证改进后的Tags质量
"""

import pandas as pd
from collections import Counter

def verify_improved_tags():
    print('🔍 验证改进后的Tags质量')
    print('='*60)
    
    # 对比三个版本的Tags
    files_to_compare = [
        {
            'name': 'Profishop-1',
            'original': 'woocommerce_output_final/woocommerce_profishop-de_final.csv',
            'first_optimized': 'woocommerce_output_final/woocommerce_profishop-de_final_optimized.csv',
            'improved': 'woocommerce_output_final/woocommerce_profishop-de_final_improved_tags.csv'
        }
    ]
    
    for file_info in files_to_compare:
        print(f'\n📊 {file_info["name"]} Tags质量对比:')
        print('-'*50)
        
        # 读取三个版本
        df_orig = pd.read_csv(file_info['original'], encoding='utf-8-sig', nrows=20)
        df_first = pd.read_csv(file_info['first_optimized'], encoding='utf-8-sig', nrows=20)
        df_improved = pd.read_csv(file_info['improved'], encoding='utf-8-sig', nrows=20)
        
        # 对比前10个产品的Tags
        for i in range(10):
            print(f'\n产品 {i+1}: {str(df_orig.iloc[i]["Name"])[:40]}...')
            
            orig_tags = str(df_orig.iloc[i]['Tags'])
            first_tags = str(df_first.iloc[i]['Tags'])
            improved_tags = str(df_improved.iloc[i]['Tags'])
            
            print(f'  原始Tags: {orig_tags[:50]}...')
            print(f'  首次优化: {first_tags}')
            print(f'  改进Tags: {improved_tags}')
            
            # 评估改进效果
            if improved_tags != 'nan' and improved_tags:
                tag_list = [tag.strip().lower() for tag in improved_tags.split(',')]
                
                # 检查是否包含产品功能关键词
                function_keywords = ['bohrer', 'säge', 'schrauber', 'hammer', 'zange', 'reinigungsgerät', 'sauger', 'kompressor', 'pumpe']
                has_function = any(any(func in tag for func in function_keywords) for tag in tag_list)
                
                # 检查是否包含特殊功能关键词
                feature_keywords = ['akku', 'elektrisch', 'hydraulisch', 'pneumatisch', 'digital']
                has_feature = any(any(feature in tag for feature in feature_keywords) for tag in tag_list)
                
                # 检查是否还有无意义的技术参数
                bad_keywords = ['mm', 'din', 'edelstahl', 'kunststoff', 'stahl']
                has_bad = any(any(bad in tag for bad in bad_keywords) for tag in tag_list)
                
                if has_function:
                    print(f'    ✅ 包含产品功能关键词')
                if has_feature:
                    print(f'    ✅ 包含特殊功能关键词')
                if has_bad:
                    print(f'    ❌ 仍包含技术参数')
                if not has_function and not has_feature and not has_bad:
                    print(f'    ⚠️ Tags质量需要进一步改进')
            else:
                print(f'    ⚠️ 没有Tags')
    
    # 统计改进后的Tags分布
    print(f'\n📈 改进后Tags分布统计')
    print('='*60)
    
    improved_file = 'woocommerce_output_final/woocommerce_profishop-de_final_improved_tags.csv'
    df_improved_full = pd.read_csv(improved_file, encoding='utf-8-sig', nrows=1000)
    
    all_improved_tags = []
    for tags in df_improved_full['Tags'].dropna():
        if str(tags) != 'nan':
            tag_list = [tag.strip() for tag in str(tags).split(',')]
            all_improved_tags.extend(tag_list)
    
    # 统计最常见的改进Tags
    improved_counter = Counter(all_improved_tags)
    
    print('最常见的改进后Tags:')
    print('-'*30)
    for tag, count in improved_counter.most_common(15):
        if tag and len(tag) > 1:
            print(f'  "{tag}" - 出现{count}次')
    
    # 分析Tags类型分布
    function_count = 0
    feature_count = 0
    application_count = 0
    other_count = 0
    
    function_keywords = ['bohrer', 'säge', 'schrauber', 'hammer', 'zange', 'reinigungsgerät', 'sauger', 'kompressor', 'pumpe', 'messgerät']
    feature_keywords = ['akku', 'elektrisch', 'hydraulisch', 'pneumatisch', 'digital', 'automatisch']
    application_keywords = ['industrie', 'profi', 'handwerk', 'bau', 'werkstatt']
    
    for tag, count in improved_counter.items():
        tag_lower = tag.lower()
        if any(func in tag_lower for func in function_keywords):
            function_count += count
        elif any(feature in tag_lower for feature in feature_keywords):
            feature_count += count
        elif any(app in tag_lower for app in application_keywords):
            application_count += count
        else:
            other_count += count
    
    total_tags = function_count + feature_count + application_count + other_count
    
    print(f'\nTags类型分布:')
    print(f'  产品功能Tags: {function_count} ({function_count/total_tags*100:.1f}%)')
    print(f'  特殊功能Tags: {feature_count} ({feature_count/total_tags*100:.1f}%)')
    print(f'  应用场景Tags: {application_count} ({application_count/total_tags*100:.1f}%)')
    print(f'  其他Tags: {other_count} ({other_count/total_tags*100:.1f}%)')
    
    print(f'\n🎯 改进效果评估')
    print('='*60)
    
    if function_count > feature_count and function_count > other_count:
        print('✅ 产品功能Tags占主导地位 - 改进成功')
    else:
        print('⚠️ 产品功能Tags比例需要提升')
    
    if other_count < total_tags * 0.3:
        print('✅ 无意义Tags比例控制良好')
    else:
        print('⚠️ 仍有较多无意义Tags需要清理')
    
    print(f'\n改进建议:')
    print('1. 继续提升产品功能关键词的识别准确率')
    print('2. 加强从产品名称中提取关键词的算法')
    print('3. 建立更完整的产品功能关键词词典')
    print('4. 考虑添加产品用途和应用场景Tags')

if __name__ == "__main__":
    verify_improved_tags()
