#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
针对Klickparts和Lampenwelt的专门优化脚本
"""

import pandas as pd
import re
from pathlib import Path

class KlickpartsLampenweltOptimizer:
    def __init__(self):
        """初始化优化器"""
        
        # Klickparts (建筑机械/汽车配件) 优化配置
        self.klickparts_config = {
            'category_keywords': {
                'Hydraulik': ['hydraulik', 'zylinder', 'dichtung', 'ventil'],
                'Motor': ['motor', 'kolben', 'kurbelwelle', 'nockenwelle'],
                'Getriebe': ['getriebe', 'kupplung', 'zahnrad', 'welle'],
                'Elektrik': ['lichtmaschine', 'anlasser', 'generator', 'elektrik'],
                'Fahrwerk': ['fahrwerk', 'achse', 'bremse', 'federung'],
                'Lager': ['lager', 'rollenlager', 'kugellager', 'nadellager'],
                'Dichtungen': ['dichtring', 'o-ring', 'simmerring', 'dichtung'],
                'Sc<PERSON><PERSON>': ['schraube', 'mutter', 'bolzen', 'stift']
            },
            'selling_points': {
                'Hydraulik': [
                    '✓ Hochdruckfeste Qualität für zuverlässige Leistung',
                    '✓ Präzise Passform & OEM-Standard Kompatibilität',
                    '✓ Langlebige Konstruktion mit Herstellergarantie'
                ],
                'Motor': [
                    '✓ OEM-Qualität für optimale Motorleistung',
                    '✓ Präzisionsgefertigt für perfekte Passgenauigkeit',
                    '✓ Geprüfte Qualität mit Langzeitgarantie'
                ],
                'Getriebe': [
                    '✓ Hochbelastbare Materialien für Dauerbetrieb',
                    '✓ Exakte Spezifikationen & perfekte Kompatibilität',
                    '✓ Professionelle Qualität mit Garantieleistung'
                ],
                'Elektrik': [
                    '✓ Zuverlässige Elektronik für störungsfreien Betrieb',
                    '✓ Hochwertige Komponenten & lange Lebensdauer',
                    '✓ Einfache Installation mit Anschlussgarantie'
                ],
                'Lager': [
                    '✓ Präzisionslager für reibungslose Funktion',
                    '✓ Hochbelastbare Ausführung & lange Standzeit',
                    '✓ Industriestandard mit Qualitätsgarantie'
                ],
                'Dichtungen': [
                    '✓ Perfekte Abdichtung & Druckbeständigkeit',
                    '✓ Temperaturresistente Materialien & Langlebigkeit',
                    '✓ Einfacher Austausch mit Dichtigkeitsgarantie'
                ],
                'Generic': [
                    '✓ OEM-Qualität für professionelle Anwendung',
                    '✓ Geprüfte Kompatibilität & zuverlässige Funktion',
                    '✓ Hochwertige Verarbeitung mit Herstellergarantie'
                ]
            }
        }
        
        # Lampenwelt (照明设备) 优化配置
        self.lampenwelt_config = {
            'category_keywords': {
                'Pendelleuchten': ['pendelleuchte', 'hängelampe', 'hängeleuchte'],
                'Wandleuchten': ['wandleuchte', 'wandlampe', 'applique'],
                'Deckenleuchten': ['deckenleuchte', 'deckenlampe', 'ceiling'],
                'Tischlampen': ['tischlampe', 'tischleuchte', 'schreibtischlampe'],
                'Stehlampen': ['stehlampe', 'stehleuchte', 'standleuchte'],
                'Außenleuchten': ['außenleuchte', 'gartenleuchte', 'outdoor'],
                'Strahler': ['strahler', 'spot', 'downlight', 'einbaustrahler'],
                'LED': ['led', 'led-lampe', 'led-röhre', 'led-strip'],
                'Bürobeleuchtung': ['büro', 'office', 'arbeitsplatz']
            },
            'selling_points': {
                'Pendelleuchten': [
                    '✓ Stilvolle Raumbeleuchtung & dekorativer Blickfang',
                    '✓ Hochwertige Materialien & langlebige LED-Technik',
                    '✓ Einfache Montage mit dimmbaren Lichtoptionen'
                ],
                'Wandleuchten': [
                    '✓ Elegante Wandbeleuchtung für perfekte Atmosphäre',
                    '✓ Energieeffiziente LED-Technologie & lange Lebensdauer',
                    '✓ Vielseitige Montage mit stilvollem Design'
                ],
                'Deckenleuchten': [
                    '✓ Optimale Raumausleuchtung & gleichmäßige Lichtverteilung',
                    '✓ Moderne LED-Technik für Energieeffizienz',
                    '✓ Hochwertige Verarbeitung & einfache Installation'
                ],
                'Tischlampen': [
                    '✓ Perfekte Arbeitsplatzbeleuchtung & Augenkomfort',
                    '✓ Flexibel einstellbar mit dimmbaren Funktionen',
                    '✓ Stilvolles Design trifft praktische Funktionalität'
                ],
                'Stehlampen': [
                    '✓ Stimmungsvolle Beleuchtung & Raumatmosphäre',
                    '✓ Hochwertige Materialien & elegantes Design',
                    '✓ Flexible Positionierung mit dimmbaren Optionen'
                ],
                'Außenleuchten': [
                    '✓ Wetterfeste Konstruktion für Outdoor-Einsatz',
                    '✓ IP-Schutz & UV-beständige Materialien',
                    '✓ Energiesparende LED-Technik mit Langzeitgarantie'
                ],
                'Strahler': [
                    '✓ Präzise Lichtlenkung & optimale Ausleuchtung',
                    '✓ Hocheffiziente LED-Spots mit geringem Verbrauch',
                    '✓ Einfache Installation & wartungsfreier Betrieb'
                ],
                'LED': [
                    '✓ Hocheffiziente LED-Technologie & Energieeinsparung',
                    '✓ Lange Lebensdauer bis zu 50.000 Betriebsstunden',
                    '✓ Sofort 100% Helligkeit ohne Aufwärmzeit'
                ],
                'Generic': [
                    '✓ Hochwertige Beleuchtungslösung & stilvolles Design',
                    '✓ Energieeffiziente Technologie & lange Lebensdauer',
                    '✓ Einfache Installation mit Qualitätsgarantie'
                ]
            }
        }
        
    def detect_klickparts_category(self, product_name, category):
        """检测Klickparts产品类别"""
        text = (str(product_name) + ' ' + str(category)).lower()
        
        for cat, keywords in self.klickparts_config['category_keywords'].items():
            for keyword in keywords:
                if keyword in text:
                    return cat
        return 'Generic'
        
    def detect_lampenwelt_category(self, product_name, category):
        """检测Lampenwelt产品类别"""
        text = (str(product_name) + ' ' + str(category)).lower()
        
        for cat, keywords in self.lampenwelt_config['category_keywords'].items():
            for keyword in keywords:
                if keyword in text:
                    return cat
        return 'Generic'
        
    def optimize_klickparts_description(self, row):
        """优化Klickparts产品描述"""
        original_desc = str(row['Short description'])
        
        # 提取产品信息部分
        if '✓' in original_desc:
            product_info = original_desc.split('✓')[0].strip()
        else:
            product_info = original_desc
            
        # 检测产品类别
        category = self.detect_klickparts_category(row['Name'], row['Categories'])
        
        # 获取对应的卖点
        selling_points = self.klickparts_config['selling_points'].get(category, 
                        self.klickparts_config['selling_points']['Generic'])
        
        # 组合新描述
        optimized_desc = product_info + ' ' + ' '.join(selling_points)
        
        return optimized_desc, category
        
    def optimize_lampenwelt_description(self, row):
        """优化Lampenwelt产品描述"""
        original_desc = str(row['Short description'])
        
        # 提取产品信息部分 - Lampenwelt的描述格式不同
        # 通常是产品名称 + 营销文案
        product_name = str(row['Name'])
        
        # 检测产品类别
        category = self.detect_lampenwelt_category(row['Name'], row['Categories'])
        
        # 获取对应的卖点
        selling_points = self.lampenwelt_config['selling_points'].get(category,
                        self.lampenwelt_config['selling_points']['Generic'])
        
        # 组合新描述 - 使用产品名称 + 优化卖点
        optimized_desc = product_name + ' ' + ' '.join(selling_points)
        
        return optimized_desc, category
        
    def optimize_file(self, file_path, file_type):
        """优化指定文件"""
        print(f"开始优化 {file_type} 文件: {Path(file_path).name}")
        
        try:
            df = pd.read_csv(file_path, encoding='utf-8-sig')
            print(f"原始数据: {len(df):,} 行")
            
            optimized_count = 0
            category_stats = {}
            
            for idx, row in df.iterrows():
                if file_type == 'Klickparts':
                    optimized_desc, category = self.optimize_klickparts_description(row)
                elif file_type == 'Lampenwelt':
                    optimized_desc, category = self.optimize_lampenwelt_description(row)
                else:
                    continue
                    
                df.at[idx, 'Short description'] = optimized_desc
                optimized_count += 1
                
                # 统计类别
                category_stats[category] = category_stats.get(category, 0) + 1
                
                # 显示进度
                if optimized_count % 5000 == 0:
                    print(f"已优化 {optimized_count:,} 个描述...")
            
            print(f"总共优化了 {optimized_count:,} 个短描述")
            
            # 显示类别统计
            print(f"\n产品类别分布:")
            for category, count in sorted(category_stats.items(), key=lambda x: x[1], reverse=True):
                percentage = count / optimized_count * 100
                print(f"  {category}: {count:,} 个 ({percentage:.1f}%)")
            
            # 保存优化后的文件
            output_file = file_path.replace('.csv', '_optimized.csv')
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            print(f"\n优化完成，保存到: {Path(output_file).name}")
            
            return output_file, optimized_count, category_stats
            
        except Exception as e:
            print(f"优化失败: {e}")
            import traceback
            traceback.print_exc()
            return None, 0, {}
            
    def preview_optimization(self, file_path, file_type, num_samples=5):
        """预览优化效果"""
        print(f"预览 {file_type} 优化效果:")
        print("="*50)
        
        try:
            df = pd.read_csv(file_path, encoding='utf-8-sig', nrows=20)
            
            for i in range(min(num_samples, len(df))):
                row = df.iloc[i]
                
                print(f"示例 {i+1}:")
                print(f"产品名称: {str(row['Name'])[:60]}...")
                print(f"原始描述: {str(row['Short description'])[:80]}...")
                
                if file_type == 'Klickparts':
                    optimized_desc, category = self.optimize_klickparts_description(row)
                elif file_type == 'Lampenwelt':
                    optimized_desc, category = self.optimize_lampenwelt_description(row)
                
                print(f"优化描述: {optimized_desc[:80]}...")
                print(f"检测类别: {category}")
                print("-" * 50)
                
        except Exception as e:
            print(f"预览失败: {e}")

def main():
    """主函数"""
    optimizer = KlickpartsLampenweltOptimizer()
    
    print("🚀 Klickparts & Lampenwelt 专门优化工具")
    print("="*60)
    
    files_to_optimize = [
        ('woocommerce_output_final/woocommerce_klickparts-de-3-op_final.csv', 'Klickparts'),
        ('woocommerce_output_final/woocommerce_lampenwelt-de_final.csv', 'Lampenwelt')
    ]
    
    for file_path, file_type in files_to_optimize:
        if Path(file_path).exists():
            print(f"\n📋 预览 {file_type} 优化效果:")
            optimizer.preview_optimization(file_path, file_type, 3)
            
            print(f"\n🔧 开始优化 {file_type}:")
            output_file, optimized_count, category_stats = optimizer.optimize_file(file_path, file_type)
            
            if output_file:
                print(f"✅ {file_type} 优化成功完成!")
            else:
                print(f"❌ {file_type} 优化失败")
        else:
            print(f"❌ 文件不存在: {file_path}")
        
        print("\n" + "="*60)

if __name__ == "__main__":
    main()
