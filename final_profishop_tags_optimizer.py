#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版Profishop Tags优化器 - 基于产品名称的智能提取
"""

import pandas as pd
import re
from pathlib import Path

class FinalProfishopTagsOptimizer:
    def __init__(self):
        """初始化最终版优化器"""
        
        # 核心产品类型词典 (从产品名称直接提取)
        self.core_product_types = {
            # 清洁设备
            'scheuersaugmaschine': 'Reinigungsmaschine',
            'hochdruckreiniger': 'Hochdruckreiniger', 
            'sauger': 'Sauger',
            'waschsauger': 'Waschsauger',
            'dampfreiniger': 'Dampfreiniger',
            'kehrmaschine': 'Kehrmaschine',
            
            # 工具类
            'bohrmaschine': 'Bohrmaschine',
            'schlagbohrmaschine': 'Schlagbohrmaschine',
            'schrauber': 'Schrauber',
            'schlagschrauber': 'Schlagschrauber',
            'säge': 'Säge',
            'kreissäge': 'Kreissäge',
            'stichsäge': 'Stichsäge',
            'hammer': 'Hammer',
            'schlagbohrer': 'Schlagbohrer',
            'winkelschleifer': 'Winkelschleifer',
            'flex': 'Winkelschleifer',
            
            # 测量设备
            'multimeter': 'Multimeter',
            'thermometer': 'Thermometer',
            'manometer': 'Manometer',
            'messgerät': 'Messgerät',
            'waage': 'Waage',
            
            # 压缩机/发电机
            'kompressor': 'Kompressor',
            'generator': 'Generator',
            'pumpe': 'Pumpe',
            
            # 安全设备
            'helm': 'Schutzhelm',
            'schutzhelm': 'Schutzhelm',
            'handschuh': 'Schutzhandschuh',
            'schutzbrille': 'Schutzbrille',
            'atemschutz': 'Atemschutz',
            
            # 存储/运输
            'koffer': 'Koffer',
            'werkzeugkoffer': 'Werkzeugkoffer',
            'wagen': 'Transportwagen',
            'leiter': 'Leiter',
            'treppe': 'Arbeitsplattform'
        }
        
        # 功能特征词典
        self.feature_keywords = {
            'akku': 'Akku',
            'akkubetrieben': 'Akku',
            'kabellos': 'Kabellos',
            'elektrisch': 'Elektrisch',
            'hydraulisch': 'Hydraulisch',
            'pneumatisch': 'Pneumatisch',
            'digital': 'Digital',
            'beheizter': 'Beheizt',
            'fahrbar': 'Fahrbar',
            'tragbar': 'Tragbar'
        }
        
        # 应用场景词典
        self.application_keywords = {
            'industrie': 'Industrie',
            'industriell': 'Industrie',
            'profi': 'Profi',
            'professional': 'Profi',
            'handwerk': 'Handwerk',
            'bau': 'Bau',
            'werkstatt': 'Werkstatt'
        }
        
    def extract_core_tags_from_name(self, product_name):
        """从产品名称提取核心Tags"""
        name_lower = str(product_name).lower()
        
        # 清理产品名称
        # 移除品牌名
        brands = ['kärcher', 'bosch', 'makita', 'rems', 'hymer', 'güde', 'jung', 'wld-tec']
        for brand in brands:
            name_lower = name_lower.replace(brand, '')
        
        # 移除型号和数字
        name_lower = re.sub(r'\b\d+[\w\d\-/]*\b', '', name_lower)
        name_lower = re.sub(r'\b[a-z]{1,2}\s*\d+\b', '', name_lower)
        
        extracted_tags = []
        
        # 1. 寻找核心产品类型 (最重要)
        for keyword, tag in self.core_product_types.items():
            if keyword in name_lower:
                extracted_tags.append(tag)
                break  # 只取第一个匹配的产品类型
        
        # 2. 寻找功能特征
        for keyword, tag in self.feature_keywords.items():
            if keyword in name_lower and tag not in extracted_tags:
                extracted_tags.append(tag)
                break  # 只取第一个功能特征
        
        # 3. 如果没有功能特征，寻找应用场景
        if len(extracted_tags) < 2:
            for keyword, tag in self.application_keywords.items():
                if keyword in name_lower and tag not in extracted_tags:
                    extracted_tags.append(tag)
                    break
        
        return extracted_tags[:2]  # 最多2个Tags
        
    def optimize_tags_final(self, product_name):
        """最终版Tags优化 - 完全基于产品名称"""
        if pd.isna(product_name) or product_name == '':
            return ''
        
        # 从产品名称提取核心Tags
        core_tags = self.extract_core_tags_from_name(product_name)
        
        # 如果没有提取到Tags，尝试更宽泛的匹配
        if not core_tags:
            name_lower = str(product_name).lower()
            
            # 通用工具识别
            if any(word in name_lower for word in ['werkzeug', 'tool']):
                core_tags.append('Werkzeug')
            elif any(word in name_lower for word in ['reinig', 'clean']):
                core_tags.append('Reinigungsgerät')
            elif any(word in name_lower for word in ['mess', 'meter']):
                core_tags.append('Messgerät')
            elif any(word in name_lower for word in ['sicherheit', 'schutz', 'safety']):
                core_tags.append('Sicherheitsausrüstung')
        
        return ', '.join(core_tags) if core_tags else ''
        
    def create_sample_optimization(self):
        """创建优化示例"""
        print('🎯 最终版Tags优化示例')
        print('='*60)
        
        examples = [
            'Kärcher Scheuersaugmaschine BD 43/35 C Ep',
            'Bosch Schlagbohrmaschine GSB 13 RE Professional',
            'Makita Akku-Schrauber DF331D 10,8V',
            'REMS Rohrschneider Cu-INOX 3-120',
            'Güde Kompressor Airpower 240/10/24',
            'HYMER Podesttreppe fahrbar einseitig begehbar',
            'WLD-Tec Thermoelement Typ K Tauchfühler',
            'JUNG Steckdose 1f weiß glänzend USB'
        ]
        
        for i, name in enumerate(examples, 1):
            optimized_tags = self.optimize_tags_final(name)
            print(f'{i}. {name[:40]}...')
            print(f'   优化Tags: {optimized_tags}')
            print()
        
    def optimize_file_final(self, file_path):
        """最终版文件优化"""
        file_name = Path(file_path).name
        print(f"开始最终版优化: {file_name}")
        
        try:
            df = pd.read_csv(file_path, encoding='utf-8-sig')
            print(f"原始数据: {len(df):,} 行")
            
            optimized_count = 0
            empty_tags = 0
            
            for idx, row in df.iterrows():
                product_name = row['Name']
                
                # 使用最终版优化
                optimized_tags = self.optimize_tags_final(product_name)
                df.at[idx, 'Tags'] = optimized_tags
                
                if optimized_tags:
                    optimized_count += 1
                else:
                    empty_tags += 1
                
                # 显示进度
                if (idx + 1) % 5000 == 0:
                    print(f"已处理 {idx + 1:,} 个产品...")
            
            print(f"Tags优化完成: {optimized_count:,} 个产品有Tags")
            print(f"空Tags: {empty_tags:,} 个产品")
            
            # 保存最终版文件
            output_file = file_path.replace('.csv', '_final_tags.csv')
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            print(f"最终版优化完成，保存到: {Path(output_file).name}")
            
            return output_file, optimized_count, empty_tags
            
        except Exception as e:
            print(f"优化失败: {e}")
            return None, 0, 0

def main():
    """主函数"""
    optimizer = FinalProfishopTagsOptimizer()
    
    print("🚀 最终版Profishop Tags优化器")
    print("="*60)
    print("优化策略:")
    print("• 完全基于产品名称提取核心功能")
    print("• 忽略原始Tags，避免噪音干扰")
    print("• 优先产品类型，其次功能特征")
    print("• 每个产品最多2个高质量Tags")
    print("="*60)
    
    # 显示优化示例
    optimizer.create_sample_optimization()
    
    print("是否要对实际文件进行优化？(y/n)")
    # 这里可以添加用户确认逻辑

if __name__ == "__main__":
    main()
