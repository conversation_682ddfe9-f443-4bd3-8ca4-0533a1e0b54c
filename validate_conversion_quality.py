#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
转换质量验证脚本 - 检查数据完整性、准确性和HTML优化
"""

import pandas as pd
import numpy as np
from pathlib import Path
import re
import html
import sys
from urllib.parse import urlparse

class ConversionQualityValidator:
    def __init__(self):
        self.issues = []
        self.warnings = []
        
    def log_issue(self, severity, category, message, file_name="", row_id=""):
        """记录问题"""
        issue = {
            'severity': severity,  # ERROR, WARNING, INFO
            'category': category,
            'message': message,
            'file': file_name,
            'row': row_id
        }
        if severity == 'ERROR':
            self.issues.append(issue)
        else:
            self.warnings.append(issue)
            
    def validate_data_integrity(self, source_file, converted_file):
        """验证数据完整性 - 检查是否有数据丢失或错位"""
        print(f"\n🔍 验证数据完整性: {Path(source_file).name}")
        
        try:
            # 读取源文件和转换文件
            if source_file.endswith('.xlsx'):
                source_df = pd.read_excel(source_file)
            else:
                source_df = pd.read_csv(source_file, encoding='utf-8-sig')
                
            converted_df = pd.read_csv(converted_file, encoding='utf-8-sig')
            
            # 1. 检查行数是否一致
            if len(source_df) != len(converted_df):
                self.log_issue('ERROR', 'DATA_LOSS', 
                             f"行数不匹配: 源文件{len(source_df)}行 vs 转换文件{len(converted_df)}行",
                             Path(source_file).name)
                             
            # 2. 检查关键字段的对应关系
            self.validate_field_mapping(source_df, converted_df, Path(source_file).name)
            
            # 3. 检查数据类型和格式
            self.validate_data_types(converted_df, Path(source_file).name)
            
            print(f"✅ 数据完整性检查完成")
            
        except Exception as e:
            self.log_issue('ERROR', 'VALIDATION_ERROR', f"验证失败: {e}", Path(source_file).name)
            
    def validate_field_mapping(self, source_df, converted_df, file_name):
        """验证字段映射的准确性"""
        print("  📋 检查字段映射...")
        
        # 检查产品名称映射
        source_name_col = self.detect_source_field(source_df, ['title', 'Name', 'product_name'])
        if source_name_col:
            # 随机抽样检查10个产品
            sample_indices = np.random.choice(len(source_df), min(10, len(source_df)), replace=False)
            
            for idx in sample_indices:
                source_name = str(source_df.iloc[idx][source_name_col]).strip()
                converted_name = str(converted_df.iloc[idx]['Name']).strip()
                
                # 检查名称是否对应（允许轻微的清理差异）
                if source_name != converted_name and len(source_name) > 0:
                    # 检查是否只是HTML清理导致的差异
                    cleaned_source = re.sub(r'<[^>]+>', '', source_name).strip()
                    if cleaned_source != converted_name:
                        self.log_issue('WARNING', 'NAME_MISMATCH',
                                     f"产品名称可能不匹配 行{idx+1}: '{source_name[:50]}...' -> '{converted_name[:50]}...'",
                                     file_name, idx+1)
                                     
        # 检查价格映射
        source_price_col = self.detect_source_field(source_df, ['price', 'regular_price', 'preis'])
        if source_price_col:
            for idx in sample_indices:
                source_price_str = str(source_df.iloc[idx][source_price_col])
                converted_price = converted_df.iloc[idx]['Regular price']
                
                if pd.notna(source_price_str) and source_price_str != 'nan':
                    # 清理源价格
                    source_price_clean = re.sub(r'[€$£¥\s]', '', source_price_str)
                    if ',' in source_price_clean and '.' in source_price_clean:
                        source_price_clean = source_price_clean.replace('.', '').replace(',', '.')
                    elif ',' in source_price_clean:
                        parts = source_price_clean.split(',')
                        if len(parts) == 2 and len(parts[1]) <= 2:
                            source_price_clean = source_price_clean.replace(',', '.')
                            
                    try:
                        source_price_num = float(source_price_clean)
                        if pd.notna(converted_price) and abs(source_price_num - float(converted_price)) > 0.01:
                            self.log_issue('ERROR', 'PRICE_MISMATCH',
                                         f"价格不匹配 行{idx+1}: {source_price_str} -> {converted_price}",
                                         file_name, idx+1)
                    except ValueError:
                        pass
                        
    def detect_source_field(self, df, field_list):
        """检测源文件中的字段"""
        for field in field_list:
            for col in df.columns:
                if field.lower() in col.lower() or col.lower() in field.lower():
                    return col
        return None
        
    def validate_data_types(self, df, file_name):
        """验证数据类型和格式"""
        print("  🔢 检查数据类型...")
        
        # 检查价格字段
        price_cols = ['Regular price', 'Sale price']
        for col in price_cols:
            if col in df.columns:
                non_empty = df[col].dropna()
                if len(non_empty) > 0:
                    # 检查是否有非数字价格
                    for idx, price in non_empty.items():
                        try:
                            float(price)
                        except (ValueError, TypeError):
                            self.log_issue('ERROR', 'INVALID_PRICE',
                                         f"无效价格格式 行{idx+1}: {price}",
                                         file_name, idx+1)
                                         
        # 检查SKU唯一性
        if 'SKU' in df.columns:
            sku_counts = df['SKU'].value_counts()
            duplicates = sku_counts[sku_counts > 1]
            if len(duplicates) > 0:
                self.log_issue('ERROR', 'DUPLICATE_SKU',
                             f"发现重复SKU: {len(duplicates)}个",
                             file_name)
                             
    def validate_html_optimization(self, converted_file):
        """验证HTML描述优化"""
        print(f"\n🧹 验证HTML优化: {Path(converted_file).name}")
        
        try:
            df = pd.read_csv(converted_file, encoding='utf-8-sig')
            
            desc_cols = ['Description', 'Short description']
            for col in desc_cols:
                if col in df.columns:
                    descriptions = df[col].dropna()
                    
                    for idx, desc in descriptions.items():
                        desc_str = str(desc)
                        
                        # 检查危险标签
                        dangerous_tags = ['<script', '<iframe', '<object', '<embed']
                        for tag in dangerous_tags:
                            if tag in desc_str.lower():
                                self.log_issue('ERROR', 'DANGEROUS_HTML',
                                             f"发现危险HTML标签 {tag} 行{idx+1}",
                                             Path(converted_file).name, idx+1)
                                             
                        # 检查事件处理器 - 使用更精确的模式避免误匹配德语单词
                        event_patterns = [
                            r'\s+on(click|load|mouse\w+|key\w+|focus|blur|change|submit)\s*=',
                            r'<[^>]*\s+on\w+\s*=',  # 在HTML标签内的事件处理器
                        ]
                        for pattern in event_patterns:
                            if re.search(pattern, desc_str, re.IGNORECASE):
                                self.log_issue('ERROR', 'DANGEROUS_EVENTS',
                                             f"发现危险事件处理器 行{idx+1}",
                                             Path(converted_file).name, idx+1)
                                break
                                         
                        # 检查HTML实体编码
                        if '&lt;' in desc_str or '&gt;' in desc_str:
                            self.log_issue('WARNING', 'HTML_ENTITIES',
                                         f"可能存在过度编码的HTML实体 行{idx+1}",
                                         Path(converted_file).name, idx+1)
                                         
                        # 检查德语字符保留
                        german_chars = ['ä', 'ö', 'ü', 'ß', 'Ä', 'Ö', 'Ü']
                        has_german = any(char in desc_str for char in german_chars)
                        if has_german:
                            # 这是好事，记录为信息
                            pass
                            
            print(f"✅ HTML优化检查完成")
            
        except Exception as e:
            self.log_issue('ERROR', 'HTML_VALIDATION_ERROR', f"HTML验证失败: {e}", Path(converted_file).name)
            
    def validate_product_attributes(self, converted_file):
        """验证产品属性准确性"""
        print(f"\n🏷️ 验证产品属性: {Path(converted_file).name}")
        
        try:
            df = pd.read_csv(converted_file, encoding='utf-8-sig')
            
            # 检查分类格式
            if 'Categories' in df.columns:
                categories = df['Categories'].dropna()
                for idx, cat in categories.items():
                    cat_str = str(cat)
                    # 检查分类格式是否合理
                    if len(cat_str.strip()) == 0:
                        self.log_issue('WARNING', 'EMPTY_CATEGORY',
                                     f"空分类 行{idx+1}",
                                     Path(converted_file).name, idx+1)
                    elif cat_str == 'General' and len(categories) > 1:
                        # 如果大部分产品都是General分类，可能映射有问题
                        pass
                        
            # 检查品牌属性
            if 'Attribute 1 name' in df.columns and 'Attribute 1 value(s)' in df.columns:
                brand_attrs = df[df['Attribute 1 name'] == 'Brand']
                empty_brands = brand_attrs[brand_attrs['Attribute 1 value(s)'].isna()]
                if len(empty_brands) > 0:
                    self.log_issue('WARNING', 'EMPTY_BRAND',
                                 f"品牌属性为空: {len(empty_brands)}个产品",
                                 Path(converted_file).name)
                                 
            # 检查图片URL格式
            if 'Images' in df.columns:
                images = df['Images'].dropna()
                for idx, img in images.items():
                    img_str = str(img)
                    urls = img_str.split(',')
                    for url in urls:
                        url = url.strip()
                        if url and not self.is_valid_url(url):
                            self.log_issue('WARNING', 'INVALID_IMAGE_URL',
                                         f"可能无效的图片URL 行{idx+1}: {url[:50]}...",
                                         Path(converted_file).name, idx+1)
                                         
            print(f"✅ 产品属性检查完成")
            
        except Exception as e:
            self.log_issue('ERROR', 'ATTRIBUTE_VALIDATION_ERROR', f"属性验证失败: {e}", Path(converted_file).name)
            
    def is_valid_url(self, url):
        """检查URL格式是否有效"""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except:
            return False
            
    def validate_conversion_batch(self, source_dir, output_dir):
        """批量验证转换质量"""
        print("🔍 开始批量转换质量验证")
        print("="*50)
        
        source_path = Path(source_dir)
        output_path = Path(output_dir)
        
        if not source_path.exists() or not output_path.exists():
            print("❌ 源目录或输出目录不存在")
            return
            
        # 获取文件对应关系
        source_files = list(source_path.glob("*.xlsx")) + list(source_path.glob("*.xls"))
        
        for source_file in source_files:
            # 找到对应的转换文件
            converted_file = output_path / f"woocommerce_{source_file.stem}_de.csv"
            
            if converted_file.exists():
                # 执行各项验证
                self.validate_data_integrity(str(source_file), str(converted_file))
                self.validate_html_optimization(str(converted_file))
                self.validate_product_attributes(str(converted_file))
            else:
                self.log_issue('ERROR', 'MISSING_OUTPUT',
                             f"找不到对应的转换文件: {converted_file.name}",
                             source_file.name)
                             
    def generate_validation_report(self):
        """生成验证报告"""
        print(f"\n📊 生成验证报告")
        print("="*50)
        
        total_issues = len(self.issues)
        total_warnings = len(self.warnings)
        
        print(f"🔴 严重问题: {total_issues}")
        print(f"🟡 警告: {total_warnings}")
        
        if total_issues == 0 and total_warnings == 0:
            print("🎉 恭喜！转换质量完美，未发现任何问题！")
            return
            
        # 按类别统计问题
        issue_categories = {}
        warning_categories = {}
        
        for issue in self.issues:
            cat = issue['category']
            issue_categories[cat] = issue_categories.get(cat, 0) + 1
            
        for warning in self.warnings:
            cat = warning['category']
            warning_categories[cat] = warning_categories.get(cat, 0) + 1
            
        if issue_categories:
            print(f"\n🔴 严重问题分类:")
            for cat, count in issue_categories.items():
                print(f"  {cat}: {count}")
                
        if warning_categories:
            print(f"\n🟡 警告分类:")
            for cat, count in warning_categories.items():
                print(f"  {cat}: {count}")
                
        # 显示前10个最重要的问题
        print(f"\n📋 主要问题详情:")
        all_problems = self.issues + self.warnings
        for i, problem in enumerate(all_problems[:10]):
            severity_icon = "🔴" if problem['severity'] == 'ERROR' else "🟡"
            print(f"{severity_icon} {problem['category']}: {problem['message']}")
            if problem['file']:
                print(f"   文件: {problem['file']}")
            if problem['row']:
                print(f"   行号: {problem['row']}")
            print()
            
        if len(all_problems) > 10:
            print(f"... 还有 {len(all_problems) - 10} 个问题")

def main():
    """主函数"""
    validator = ConversionQualityValidator()
    
    print("🔍 WooCommerce转换质量验证工具")
    print("="*50)
    
    # 验证德国数据转换
    source_dir = "源数据文件/de"
    output_dir = "woocommerce_output_de"
    
    if Path(source_dir).exists() and Path(output_dir).exists():
        validator.validate_conversion_batch(source_dir, output_dir)
        validator.generate_validation_report()
    else:
        print(f"❌ 目录不存在: {source_dir} 或 {output_dir}")

if __name__ == "__main__":
    main()
