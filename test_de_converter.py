#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
德国数据转换器测试脚本
"""

import pandas as pd
from pathlib import Path
import sys
import os

# 添加当前目录到路径
sys.path.append('.')

from convert_de_to_woocommerce import GermanWooCommerceConverter

def test_single_file():
    """测试单个文件转换"""
    print("🧪 测试单个文件转换")
    print("="*40)
    
    converter = GermanWooCommerceConverter()
    
    # 测试文件列表
    test_files = [
        "源数据文件/de/bauhaus-at-de-图片前两图.xlsx",
        "源数据文件/de/beliani-de-3-or-en.xlsx",
        "源数据文件/de/lampenwelt-de.xlsx"
    ]
    
    for test_file in test_files:
        if Path(test_file).exists():
            print(f"\n📁 测试文件: {test_file}")
            
            # 只转换前100行进行测试
            try:
                if test_file.endswith('.xlsx'):
                    df = pd.read_excel(test_file, nrows=100)
                else:
                    df = pd.read_csv(test_file, nrows=100, encoding='utf-8-sig')
                    
                # 保存测试数据
                test_data_file = f"test_data_{Path(test_file).stem}.xlsx"
                df.to_excel(test_data_file, index=False)
                
                # 转换测试数据
                result = converter.convert_file(test_data_file)
                
                if result:
                    print(f"✅ 转换成功: {result}")
                    
                    # 验证输出
                    wc_df = pd.read_csv(result, encoding='utf-8-sig')
                    print(f"📊 输出统计:")
                    print(f"  产品数量: {len(wc_df)}")
                    print(f"  有价格的产品: {wc_df['Regular price'].notna().sum()}")
                    print(f"  有促销价的产品: {wc_df['Sale price'].notna().sum()}")
                    print(f"  有图片的产品: {wc_df['Images'].notna().sum()}")
                    print(f"  有描述的产品: {wc_df['Description'].notna().sum()}")
                    
                    # 显示前3行示例
                    print(f"\n📋 前3行示例:")
                    sample_cols = ['SKU', 'Name', 'Regular price', 'Categories', 'Attribute 1 name']
                    available_cols = [col for col in sample_cols if col in wc_df.columns]
                    print(wc_df[available_cols].head(3).to_string(index=False))
                    
                else:
                    print(f"❌ 转换失败")
                    
                # 清理测试文件
                if Path(test_data_file).exists():
                    os.remove(test_data_file)
                    
            except Exception as e:
                print(f"❌ 测试失败: {e}")
                import traceback
                traceback.print_exc()
                
            break  # 只测试第一个可用文件
        else:
            print(f"⚠️  文件不存在: {test_file}")

def test_field_detection():
    """测试字段检测功能"""
    print("\n🔍 测试字段检测功能")
    print("="*40)
    
    converter = GermanWooCommerceConverter()
    
    # 创建测试数据
    test_data = {
        'ID': [1, 2, 3],
        'title': ['德语产品名称 1', 'Deutsches Produkt 2', 'German Product 3'],
        'price': ['29,95', '1.234,56', '€ 45.99'],
        'sale_price': ['24,96', '999,99', ''],
        'description': ['<p>德语描述 mit HTML</p>', '<h2>Produktbeschreibung</h2>', 'Simple text'],
        'category': ['Farben & Tapeten>Tapeten', 'Möbel>Küche', 'Beleuchtung>Lampen'],
        'Brand': ['Papermoon', 'IKEA', 'Philips'],
        'image': ['https://example.com/1.jpg,https://example.com/2.jpg', 'https://test.de/image.png', '']
    }
    
    df = pd.DataFrame(test_data)
    
    # 测试字段检测
    name_col = converter.detect_field(df, converter.field_mappings['name_fields'])
    price_col = converter.detect_field(df, converter.field_mappings['price_fields'])
    category_col = converter.detect_field(df, converter.field_mappings['category_fields'])
    brand_col = converter.detect_field(df, converter.field_mappings['brand_fields'])
    
    print(f"字段检测结果:")
    print(f"  产品名称: {name_col}")
    print(f"  价格: {price_col}")
    print(f"  分类: {category_col}")
    print(f"  品牌: {brand_col}")
    
    # 测试价格清理
    print(f"\n💰 价格清理测试:")
    for price in test_data['price']:
        cleaned = converter.clean_german_price(price)
        print(f"  '{price}' -> {cleaned}")
        
    # 测试分类翻译
    print(f"\n🏷️  分类翻译测试:")
    for category in test_data['category']:
        translated = converter.translate_category(category)
        print(f"  '{category}' -> '{translated}'")
        
    # 测试SKU生成
    print(f"\n🔖 SKU生成测试:")
    for i, row in df.iterrows():
        sku = converter.generate_german_sku(row['title'], row['Brand'], i+1)
        print(f"  {row['title'][:30]}... -> {sku}")

def test_html_cleaning():
    """测试HTML清理功能"""
    print("\n🧹 测试HTML清理功能")
    print("="*40)
    
    converter = GermanWooCommerceConverter()
    
    test_html = [
        '<p>Einfacher deutscher Text mit Umlauten: äöüß</p>',
        '<h2>Produktbeschreibung</h2><p>Mit <strong>fetten</strong> Text</p>',
        '<script>alert("dangerous")</script><p>Sicherer Inhalt</p>',
        '<p onclick="hack()">Gefährlicher Event Handler</p>',
        '<table class="table"><tr><th>Eigenschaft</th><td>Wert</td></tr></table>'
    ]
    
    for html in test_html:
        cleaned = converter.clean_german_html(html)
        print(f"原始: {html[:50]}...")
        print(f"清理: {cleaned[:50]}...")
        print()

def main():
    """主测试函数"""
    print("🇩🇪 德国数据转换器测试套件")
    print("="*50)
    
    # 检查依赖
    try:
        import pandas as pd
        import numpy as np
        print("✅ 依赖检查通过")
    except ImportError as e:
        print(f"❌ 依赖缺失: {e}")
        return
        
    # 运行测试
    test_field_detection()
    test_html_cleaning()
    test_single_file()
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    main()
