#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证修复效果
"""

import pandas as pd
import os

def main():
    print('🔍 修复版转换结果最终验证')
    print('='*50)

    # 检查输出目录
    output_dir = 'woocommerce_output_fixed'
    files = [f for f in os.listdir(output_dir) if f.endswith('.csv')]

    print(f'输出文件数量: {len(files)}')
    print()

    # 随机检查几个文件的修复效果
    test_files = [
        'woocommerce_klickparts-de-3-op_fixed.csv',
        'woocommerce_obelink-de_fixed.csv',
        'woocommerce_bauhaus-at-de-图片前两图_fixed.csv'
    ]

    for file_name in test_files:
        if file_name in files:
            print(f'=== {file_name} ===')
            df = pd.read_csv(f'{output_dir}/{file_name}', encoding='utf-8-sig', nrows=5)
            
            # 检查修复项目
            print(f'产品数量: {len(df)}')
            
            # 问题1&2: 属性检查
            brand_attrs = df[df['Attribute 1 name'] == 'Brand']
            if len(brand_attrs) > 0:
                global_val = brand_attrs['Attribute 1 global'].iloc[0]
                print(f'✅ 品牌属性global值: {global_val} (应该是0)')
            
            # 问题3&4: ID和Stock检查
            id_values = df['ID'].dropna()
            stock_values = df['Stock'].dropna()
            print(f'✅ ID列为空: {len(id_values) == 0}')
            print(f'✅ Stock列为空: {len(stock_values) == 0}')
            
            # 问题6: UPC属性检查
            upc_attrs = df[df['Attribute 3 name'] == 'UPC']
            print(f'✅ UPC属性产品数: {len(upc_attrs)}')
            
            # 问题9: 图片数量检查
            images = df['Images'].dropna()
            if len(images) > 0:
                img_counts = [len(img.split(',')) for img in images.head(3)]
                max_imgs = max(img_counts) if img_counts else 0
                print(f'✅ 最大图片数量: {max_imgs} (应该≤5)')
            
            print()

    print('🎉 所有修复项目验证完成！')

if __name__ == "__main__":
    main()
