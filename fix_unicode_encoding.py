#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复CSV文件中的Unicode编码问题
"""

import pandas as pd
import re
import os
from pathlib import Path

def fix_unicode_in_text(text):
    """修复文本中的Unicode编码问题"""
    if pd.isna(text) or text == '':
        return text
    
    text_str = str(text)
    
    # 检查是否包含Unicode转义序列
    if '\\u00' in text_str:
        try:
            # 尝试解码Unicode转义序列
            fixed_text = text_str.encode().decode('unicode_escape')
            return fixed_text
        except Exception as e:
            print(f"解码失败: {text_str[:50]}... 错误: {e}")
            return text_str
    
    return text_str

def analyze_unicode_issues(file_path):
    """分析文件中的Unicode编码问题"""
    print(f"分析文件: {Path(file_path).name}")
    
    try:
        df = pd.read_csv(file_path, encoding='utf-8-sig', nrows=100)
        
        unicode_issues = []
        
        for col in df.columns:
            for idx, value in df[col].items():
                if pd.notna(value):
                    value_str = str(value)
                    if '\\u00' in value_str:
                        unicode_issues.append({
                            'row': idx + 1,
                            'column': col,
                            'original': value_str,
                            'fixed': fix_unicode_in_text(value_str)
                        })
        
        print(f"发现 {len(unicode_issues)} 个Unicode编码问题")
        
        if unicode_issues:
            print("\n前5个问题示例:")
            for i, issue in enumerate(unicode_issues[:5]):
                print(f"{i+1}. 行{issue['row']}, 列{issue['column']}:")
                print(f"   原始: {issue['original'][:80]}...")
                print(f"   修复: {issue['fixed'][:80]}...")
                print()
        
        return len(unicode_issues) > 0
        
    except Exception as e:
        print(f"分析失败: {e}")
        return False

def fix_unicode_in_csv(file_path, output_path=None):
    """修复CSV文件中的Unicode编码问题"""
    print(f"修复文件: {Path(file_path).name}")
    
    try:
        # 读取整个文件
        df = pd.read_csv(file_path, encoding='utf-8-sig')
        
        print(f"原始数据: {len(df)} 行, {len(df.columns)} 列")
        
        fixed_count = 0
        
        # 遍历所有列和行
        for col in df.columns:
            for idx in df.index:
                original_value = df.at[idx, col]
                
                if pd.notna(original_value):
                    original_str = str(original_value)
                    
                    if '\\u00' in original_str:
                        fixed_value = fix_unicode_in_text(original_str)
                        if fixed_value != original_str:
                            df.at[idx, col] = fixed_value
                            fixed_count += 1
                            
                            # 显示修复进度
                            if fixed_count % 100 == 0:
                                print(f"已修复 {fixed_count} 个Unicode问题...")
        
        print(f"总共修复了 {fixed_count} 个Unicode编码问题")
        
        # 保存修复后的文件
        if output_path is None:
            output_path = file_path.replace('.csv', '_unicode_fixed.csv')
        
        df.to_csv(output_path, index=False, encoding='utf-8-sig')
        
        print(f"修复完成，保存到: {output_path}")
        
        return output_path, fixed_count
        
    except Exception as e:
        print(f"修复失败: {e}")
        import traceback
        traceback.print_exc()
        return None, 0

def fix_all_csv_files(directory):
    """修复目录中所有CSV文件的Unicode问题"""
    print(f"批量修复目录: {directory}")
    print("="*50)
    
    csv_files = list(Path(directory).glob("*.csv"))
    
    if not csv_files:
        print("未找到CSV文件")
        return
    
    total_fixed = 0
    fixed_files = []
    
    for i, file_path in enumerate(csv_files, 1):
        print(f"\n[{i}/{len(csv_files)}] " + "="*30)
        
        # 先分析是否有问题
        has_issues = analyze_unicode_issues(str(file_path))
        
        if has_issues:
            # 直接覆盖原文件
            output_path, fixed_count = fix_unicode_in_csv(str(file_path), str(file_path))
            
            if output_path:
                fixed_files.append(output_path)
                total_fixed += fixed_count
        else:
            print("✅ 文件无Unicode编码问题")
    
    print(f"\n🎉 批量修复完成!")
    print(f"处理文件: {len(csv_files)}")
    print(f"修复文件: {len(fixed_files)}")
    print(f"总修复数: {total_fixed}")
    
    if fixed_files:
        print(f"\n修复的文件:")
        for file_path in fixed_files:
            print(f"  ✅ {Path(file_path).name}")

def main():
    """主函数"""
    print("🔧 Unicode编码修复工具")
    print("="*40)
    
    # 检查lampenwelt文件
    lampenwelt_file = "woocommerce_output_final/woocommerce_lampenwelt-de_final.csv"
    
    if Path(lampenwelt_file).exists():
        print("检查lampenwelt文件...")
        has_issues = analyze_unicode_issues(lampenwelt_file)
        
        if has_issues:
            print("\n开始修复...")
            output_path, fixed_count = fix_unicode_in_csv(lampenwelt_file, lampenwelt_file)
            
            if output_path:
                print(f"✅ lampenwelt文件修复完成，修复了 {fixed_count} 个问题")
            else:
                print("❌ lampenwelt文件修复失败")
        else:
            print("✅ lampenwelt文件无Unicode问题")
    else:
        print(f"❌ 文件不存在: {lampenwelt_file}")
    
    # 询问是否修复所有文件
    print(f"\n是否检查并修复所有final文件？")
    output_dir = "woocommerce_output_final"
    
    if Path(output_dir).exists():
        fix_all_csv_files(output_dir)
    else:
        print(f"❌ 目录不存在: {output_dir}")

if __name__ == "__main__":
    main()
