#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析当前Tags优化的问题并提供改进方案
"""

import pandas as pd
from collections import Counter

def analyze_current_tags_issues():
    print('🔍 分析当前Tags优化问题')
    print('='*60)
    
    # 读取优化后的文件分析Tags质量
    file_path = 'woocommerce_output_final/woocommerce_profishop-de_final_optimized.csv'
    df = pd.read_csv(file_path, encoding='utf-8-sig', nrows=100)
    
    print('当前优化后的Tags示例:')
    print('-'*40)
    
    problematic_tags = []
    good_tags = []
    
    for i, tags in enumerate(df['Tags'].head(20)):
        if pd.notna(tags) and tags != '':
            print(f'{i+1:2d}. {tags}')
            
            # 分析Tags质量
            tag_list = [tag.strip() for tag in str(tags).split(',')]
            for tag in tag_list:
                if tag:
                    # 问题Tags: 纯技术参数、材料名、品牌重复等
                    if any(issue in tag.lower() for issue in ['mm', 'din', 'edelstahl', 'kunststoff']):
                        problematic_tags.append(tag)
                    # 好的Tags: 产品功能、用途等
                    elif any(good in tag.lower() for good in ['werkzeug', 'bohrer', 'säge', 'schrauber']):
                        good_tags.append(tag)
    
    print(f'\n❌ 问题Tags统计:')
    problem_counter = Counter(problematic_tags)
    for tag, count in problem_counter.most_common(10):
        print(f'  "{tag}" - {count}次 (过于技术化/通用)')
    
    print(f'\n✅ 好的Tags统计:')
    good_counter = Counter(good_tags)
    for tag, count in good_counter.most_common(10):
        print(f'  "{tag}" - {count}次 (产品功能明确)')
    
    return problematic_tags, good_tags

def analyze_product_names_for_better_tags():
    """分析产品名称，提取更好的Tags"""
    print(f'\n📦 从产品名称提取更好的Tags')
    print('='*60)
    
    file_path = 'woocommerce_output_final/woocommerce_profishop-de_final.csv'  # 原始文件
    df = pd.read_csv(file_path, encoding='utf-8-sig', nrows=200)
    
    # 分析产品名称中的关键词
    product_keywords = []
    
    for name in df['Name'].head(100):
        name_str = str(name).lower()
        words = name_str.split()
        
        # 提取有意义的产品关键词
        meaningful_words = []
        for word in words:
            # 过滤掉数字、型号、品牌等
            if (len(word) > 3 and 
                not word.isdigit() and 
                not any(char.isdigit() for char in word[:3]) and
                word not in ['kärcher', 'bosch', 'makita', 'rems', 'hymer']):
                meaningful_words.append(word)
        
        product_keywords.extend(meaningful_words[:2])  # 每个产品取前2个关键词
    
    # 统计最有意义的产品关键词
    keyword_counter = Counter(product_keywords)
    
    print('从产品名称提取的有意义关键词:')
    print('-'*40)
    for keyword, count in keyword_counter.most_common(20):
        print(f'  "{keyword}" - 出现{count}次')
    
    return keyword_counter

def generate_improved_tags_strategy():
    """生成改进的Tags策略"""
    print(f'\n💡 改进的Tags优化策略')
    print('='*60)
    
    print('问题分析:')
    print('1. 当前保留的Tags过于技术化 (DIN, mm, Edelstahl)')
    print('2. 缺乏产品功能和用途描述')
    print('3. 品牌信息重复 (REMS出现多次)')
    print('4. 材料信息不是最佳的SEO关键词')
    
    print(f'\n改进方案:')
    print('1. 优先级重新排序:')
    print('   • 第一优先: 产品功能/用途 (bohrer, säge, werkzeug)')
    print('   • 第二优先: 应用场景 (industrie, bau, handwerk)')
    print('   • 第三优先: 品牌 (但避免重复)')
    print('   • 最后考虑: 材料/规格 (仅当特别重要时)')
    
    print(f'\n2. 新的关键词分类:')
    
    # 产品功能关键词 (最重要)
    function_keywords = {
        'bohrer': '钻头/钻孔工具',
        'säge': '锯/切割工具', 
        'schrauber': '螺丝刀/拧紧工具',
        'hammer': '锤子/敲击工具',
        'zange': '钳子/夹持工具',
        'messer': '刀具/切削工具',
        'werkzeug': '通用工具',
        'reinigungsgerät': '清洁设备',
        'kompressor': '压缩机',
        'pumpe': '泵类设备'
    }
    
    # 应用场景关键词
    application_keywords = {
        'industrie': '工业应用',
        'handwerk': '手工艺/维修',
        'bau': '建筑施工',
        'garten': '园艺/户外',
        'haushalt': '家用',
        'profi': '专业级'
    }
    
    # 特殊功能关键词
    feature_keywords = {
        'akku': '电池供电',
        'elektrisch': '电动',
        'hydraulisch': '液压',
        'pneumatisch': '气动',
        'digital': '数字化',
        'automatisch': '自动化'
    }
    
    print('   产品功能关键词 (第一优先):')
    for keyword, desc in list(function_keywords.items())[:5]:
        print(f'     • {keyword} - {desc}')
    
    print('   应用场景关键词 (第二优先):')
    for keyword, desc in list(application_keywords.items())[:3]:
        print(f'     • {keyword} - {desc}')
    
    print('   特殊功能关键词 (第三优先):')
    for keyword, desc in list(feature_keywords.items())[:3]:
        print(f'     • {keyword} - {desc}')
    
    return function_keywords, application_keywords, feature_keywords

def create_improved_optimizer_preview():
    """创建改进版优化器的预览"""
    print(f'\n🚀 改进版Tags优化预览')
    print('='*60)
    
    # 示例产品名称和当前Tags
    examples = [
        {
            'name': 'Kärcher Scheuersaugmaschine BD 43/35 C Ep',
            'current_tags': 'Bodenreinigungsgerät',
            'improved_tags': 'Reinigungsgerät, Industrie'
        },
        {
            'name': 'REMS Rohrschneider Cu-INOX 3-120',
            'current_tags': 'REMS',
            'improved_tags': 'Rohrschneider, Profi'
        },
        {
            'name': 'Bosch Schlagbohrmaschine GSB 13 RE',
            'current_tags': 'mm',
            'improved_tags': 'Bohrmaschine, Handwerk'
        },
        {
            'name': 'Makita Akku-Schrauber DF331D',
            'current_tags': 'Edelstahl',
            'improved_tags': 'Schrauber, Akku'
        }
    ]
    
    print('优化效果对比:')
    print('-'*60)
    
    for i, example in enumerate(examples, 1):
        print(f'{i}. 产品: {example["name"][:40]}...')
        print(f'   当前Tags: {example["current_tags"]}')
        print(f'   改进Tags: {example["improved_tags"]}')
        print(f'   改进说明: 突出产品功能和应用场景')
        print()

def main():
    """主函数"""
    print('🔍 Profishop Tags优化问题分析与改进方案')
    print('='*70)
    
    # 分析当前Tags问题
    problematic_tags, good_tags = analyze_current_tags_issues()
    
    # 从产品名称分析更好的关键词
    product_keywords = analyze_product_names_for_better_tags()
    
    # 生成改进策略
    function_kw, application_kw, feature_kw = generate_improved_tags_strategy()
    
    # 预览改进效果
    create_improved_optimizer_preview()
    
    print(f'\n📋 总结与建议')
    print('='*70)
    print('当前问题:')
    print('❌ 保留了过多技术参数Tags (DIN, mm, Edelstahl)')
    print('❌ 缺乏产品功能描述Tags')
    print('❌ 品牌Tags重复且无意义')
    print('❌ SEO价值低的材料Tags')
    
    print(f'\n解决方案:')
    print('✅ 重新设计Tags优先级算法')
    print('✅ 优先保留产品功能关键词')
    print('✅ 添加应用场景Tags')
    print('✅ 智能品牌Tags去重')
    print('✅ 提升Tags的SEO和用户价值')
    
    print(f'\n下一步行动:')
    print('1. 创建改进版Tags优化脚本')
    print('2. 重新处理6个Profishop文件')
    print('3. 验证新Tags的质量和相关性')

if __name__ == "__main__":
    main()
