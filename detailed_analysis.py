#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细分析HTML、图片和分类问题
"""

import pandas as pd
import re
from pathlib import Path

def analyze_html_tags():
    """分析HTML标签使用情况"""
    print('=== 问题1：HTML标签分析 ===')
    
    # 检查源文件中的HTML标签
    source_files = [
        '源数据文件/de/bauhaus-at-de-图片前两图.xlsx',
        '源数据文件/de/lampenwelt-de.xlsx',
        '源数据文件/de/obelink-de.xlsx'
    ]
    
    html_tags_found = {}
    
    for file_path in source_files:
        if Path(file_path).exists():
            try:
                df = pd.read_excel(file_path, nrows=20)
                print(f'\n--- {Path(file_path).name} ---')
                
                # 查找描述字段
                desc_cols = [col for col in df.columns if 'description' in col.lower() or 'desc' in col.lower()]
                
                for col in desc_cols:
                    descriptions = df[col].dropna().head(5)
                    print(f'\n{col} HTML标签示例:')
                    
                    for i, desc in enumerate(descriptions):
                        desc_str = str(desc)
                        # 提取HTML标签
                        tags = re.findall(r'<(\w+)[^>]*>', desc_str)
                        if tags:
                            print(f'  第{i+1}行: {tags}')
                            print(f'    内容: {desc_str[:100]}...')
                            
                            for tag in tags:
                                html_tags_found[tag] = html_tags_found.get(tag, 0) + 1
                        
            except Exception as e:
                print(f'读取 {file_path} 失败: {e}')
    
    print(f'\n所有HTML标签统计:')
    for tag, count in sorted(html_tags_found.items(), key=lambda x: x[1], reverse=True):
        print(f'  <{tag}>: {count}次')

def analyze_short_description():
    """分析短描述处理"""
    print('\n=== 问题1.2：短描述分析 ===')
    
    # 检查obelink文件的短描述
    source_file = '源数据文件/de/obelink-de.xlsx'
    output_file = 'woocommerce_output_fixed/woocommerce_obelink-de_fixed.csv'
    
    if Path(source_file).exists() and Path(output_file).exists():
        source_df = pd.read_excel(source_file, nrows=10)
        output_df = pd.read_csv(output_file, encoding='utf-8-sig', nrows=10)
        
        print('源文件短描述情况:')
        short_desc_col = 'Short description'
        if short_desc_col in source_df.columns:
            empty_count = source_df[short_desc_col].isna().sum()
            total_count = len(source_df)
            print(f'  空短描述: {empty_count}/{total_count}')
            
            # 检查非空的短描述
            non_empty = source_df[short_desc_col].dropna()
            if len(non_empty) > 0:
                print(f'  非空短描述示例: {non_empty.iloc[0][:50]}...')
        
        print('\n输出文件短描述情况:')
        output_short = output_df['Short description'].dropna()
        print(f'  生成的短描述数量: {len(output_short)}')
        if len(output_short) > 0:
            print(f'  示例: {output_short.iloc[0][:50]}...')

def analyze_images():
    """分析图片提取情况"""
    print('\n=== 问题2：图片提取分析 ===')
    
    test_files = [
        ('源数据文件/de/bauhaus-at-de-图片前两图.xlsx', 'woocommerce_output_fixed/woocommerce_bauhaus-at-de-图片前两图_fixed.csv'),
        ('源数据文件/de/lampenwelt-de.xlsx', 'woocommerce_output_fixed/woocommerce_lampenwelt-de_fixed.csv')
    ]
    
    for source_file, output_file in test_files:
        if Path(source_file).exists() and Path(output_file).exists():
            print(f'\n--- {Path(source_file).name} ---')
            
            source_df = pd.read_excel(source_file, nrows=10)
            output_df = pd.read_csv(output_file, encoding='utf-8-sig', nrows=10)
            
            # 找到图片字段
            img_cols = [col for col in source_df.columns if 'image' in col.lower() or 'photo' in col.lower()]
            
            for col in img_cols:
                print(f'\n{col} 图片分析:')
                
                for i in range(min(5, len(source_df))):
                    source_img = str(source_df.iloc[i][col])
                    output_img = str(output_df.iloc[i]['Images'])
                    
                    # 计算源文件图片数量
                    source_count = 0
                    if source_img and source_img != 'nan':
                        # 尝试不同分隔符
                        for sep in [',', ';', '|', '\n']:
                            if sep in source_img:
                                source_count = len(source_img.split(sep))
                                break
                        else:
                            source_count = 1 if source_img else 0
                    
                    # 计算输出文件图片数量
                    output_count = 0
                    if output_img and output_img != 'nan':
                        output_count = len(output_img.split(','))
                    
                    print(f'  第{i+1}行: 源{source_count}张 -> 输出{output_count}张')
                    
                    if source_count > 5:
                        print(f'    源文件超过5张，应该截取前5张')
                    if output_count != min(source_count, 5) and source_count > 0:
                        print(f'    ⚠️ 图片数量不匹配!')

def analyze_categories():
    """分析分类处理情况"""
    print('\n=== 问题3：分类处理分析 ===')
    
    test_files = [
        '源数据文件/de/bauhaus-at-de-图片前两图.xlsx',
        '源数据文件/de/obelink-de.xlsx'
    ]
    
    for source_file in test_files:
        if Path(source_file).exists():
            print(f'\n--- {Path(source_file).name} ---')
            
            df = pd.read_excel(source_file, nrows=20)
            
            # 找到分类字段
            cat_cols = [col for col in df.columns if 'categor' in col.lower() or 'cate' in col.lower()]
            
            for col in cat_cols:
                print(f'\n{col} 分类示例:')
                categories = df[col].dropna().head(10)
                
                comma_categories = []
                for i, cat in enumerate(categories):
                    cat_str = str(cat)
                    print(f'  第{i+1}行: {cat_str}')
                    
                    if ',' in cat_str:
                        comma_categories.append(cat_str)
                
                if comma_categories:
                    print(f'\n包含逗号的分类 ({len(comma_categories)}个):')
                    for cat in comma_categories[:5]:
                        print(f'  "{cat}"')
                        # 分析逗号的含义
                        parts = cat.split(',')
                        print(f'    分割后: {parts}')
                        
                        # 判断是否是层级关系
                        if len(parts) == 2:
                            part1, part2 = parts[0].strip(), parts[1].strip()
                            if part1 in part2 or part2 in part1:
                                print(f'    可能是层级关系: {part1} > {part2}')
                            else:
                                print(f'    可能是并列关系: {part1} 或 {part2}')

def main():
    """主函数"""
    print('🔍 详细问题分析')
    print('='*60)
    
    analyze_html_tags()
    analyze_short_description()
    analyze_images()
    analyze_categories()
    
    print('\n🎯 分析完成!')

if __name__ == "__main__":
    main()
