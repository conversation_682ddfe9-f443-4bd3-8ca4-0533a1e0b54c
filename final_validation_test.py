#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终修复效果验证
"""

import pandas as pd
import re

def test_final_fixes():
    print('🔍 最终修复效果验证')
    print('='*50)
    
    # 读取最终修复版文件
    final_df = pd.read_csv('woocommerce_obelink-de_final.csv', encoding='utf-8-sig', nrows=10)
    
    # 读取源文件对比
    source_df = pd.read_excel('源数据文件/de/obelink-de.xlsx', nrows=10)
    
    print('=== 问题1：短描述修复验证 ===')
    source_short_empty = source_df['Short description'].isna().sum()
    final_short_empty = final_df['Short description'].isna().sum() + (final_df['Short description'] == '').sum()
    
    print(f'源文件空短描述: {source_short_empty}/10')
    print(f'最终文件空短描述: {final_short_empty}/10')
    
    if source_short_empty == final_short_empty:
        print('✅ 短描述修复成功：源文件为空时不生成')
    else:
        print('❌ 短描述修复失败')
        
    print()
    
    print('=== 问题1：HTML标签转换验证 ===')
    descriptions = final_df['Description'].dropna().head(3)
    
    wc_friendly_tags = ['<p>', '<h3>', '<ul>', '<li>', '<strong>', '<em>', '<div>', '<table>']
    complex_tags = ['<h1>', '<h2>', '<section>', '<dl>', '<dt>', '<dd>']
    
    for i, desc in enumerate(descriptions):
        desc_str = str(desc)
        print(f'第{i+1}行描述HTML标签检查:')
        
        # 检查是否包含WooCommerce友好标签
        friendly_found = [tag for tag in wc_friendly_tags if tag in desc_str]
        complex_found = [tag for tag in complex_tags if tag in desc_str]
        
        print(f'  WC友好标签: {friendly_found}')
        print(f'  复杂标签: {complex_found}')
        
        if friendly_found and not complex_found:
            print('  ✅ HTML标签转换成功')
        elif complex_found:
            print('  ⚠️ 仍包含复杂标签')
        else:
            print('  ℹ️ 无HTML标签')
        print()
    
    print('=== 问题2：图片提取验证 ===')
    # 检查源文件图片
    source_images = source_df['Images'].dropna().head(5)
    final_images = final_df['Images'].dropna().head(5)
    
    for i in range(min(len(source_images), len(final_images))):
        source_img = str(source_images.iloc[i])
        final_img = str(final_images.iloc[i])
        
        # 计算源文件图片数量
        source_count = len(source_img.split(',')) if source_img else 0
        final_count = len(final_img.split(',')) if final_img else 0
        
        print(f'第{i+1}行图片: 源{source_count}张 -> 最终{final_count}张')
        
        if final_count <= 5:
            print('  ✅ 图片数量限制正确')
        else:
            print('  ❌ 图片数量超过5张')
            
        if source_count > 0 and final_count == min(source_count, 5):
            print('  ✅ 图片提取数量正确')
        elif source_count > 0:
            print('  ⚠️ 图片提取数量可能不正确')
        print()
    
    print('=== 问题3：分类处理验证 ===')
    categories = final_df['Categories'].dropna().head(5)
    
    for i, cat in enumerate(categories):
        cat_str = str(cat)
        print(f'第{i+1}行分类: {cat_str}')
        
        if ',' in cat_str and ' & ' in cat_str:
            print('  ✅ 逗号正确替换为 & (并列关系)')
        elif ',' in cat_str:
            print('  ⚠️ 仍包含逗号，可能需要处理')
        else:
            print('  ✅ 分类格式正确')
        print()
    
    print('=== 其他修复项验证 ===')
    
    # ID列检查
    id_empty = (final_df['ID'] == '').sum()
    print(f'ID列为空: {id_empty}/10 ✅' if id_empty == 10 else f'ID列为空: {id_empty}/10 ❌')
    
    # Stock列检查
    stock_empty = (final_df['Stock'] == '').sum()
    print(f'Stock列为空: {stock_empty}/10 ✅' if stock_empty == 10 else f'Stock列为空: {stock_empty}/10 ❌')
    
    # 属性global值检查
    brand_attrs = final_df[final_df['Attribute 1 name'] == 'Brand']
    if len(brand_attrs) > 0:
        global_val = brand_attrs['Attribute 1 global'].iloc[0]
        print(f'品牌属性global值: {global_val} ✅' if global_val == 0 else f'品牌属性global值: {global_val} ❌')
    
    # UPC属性检查
    upc_attrs = final_df[final_df['Attribute 3 name'] == 'UPC']
    print(f'UPC属性产品数: {len(upc_attrs)} ✅')
    
    print()
    print('🎉 最终修复效果验证完成！')

if __name__ == "__main__":
    test_final_fixes()
