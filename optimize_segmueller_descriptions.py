#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化Segmueller产品短描述的卖点
"""

import pandas as pd
import re
from pathlib import Path

class SegmuellerDescriptionOptimizer:
    def __init__(self):
        """初始化优化器"""
        
        # 产品类别关键词映射
        self.category_keywords = {
            'Sessel': ['sessel', 'stuhl', 'chair'],
            'Bett': ['bett', 'bed', 'matratze'],
            'Schrank': ['schrank', 'kleiderschrank', 'wardrobe'],
            'Sofa': ['sofa', 'couch', 'ecksofa'],
            'Tisch': ['tisch', 'table', 'esstisch'],
            'Regal': ['regal', 'shelf', 'bookshelf'],
            'Kommode': ['kommode', 'sideboard', 'chest'],
            'Lampe': ['lampe', 'leuchte', 'light'],
            'Spiegel': ['spiegel', 'mirror'],
            'Garten': ['garten', 'outdoor', 'garden']
        }
        
        # 优化的卖点方案
        self.optimized_selling_points = {
            'Sessel': [
                '✓ Ergonomisches Design für optimalen Komfort',
                '✓ Hochwertige Materialien & Verarbeitung',
                '✓ Langlebige Qualität mit Garantie'
            ],
            'Bett': [
                '✓ Erholsamer Schlaf durch optimale Ergonomie',
                '✓ Schadstofffreie & atmungsaktive Materialien',
                '✓ Robuste Konstruktion für jahrelange Nutzung'
            ],
            'Schrank': [
                '✓ Maximaler Stauraum durch clevere Aufteilung',
                '✓ Hochwertige Beschläge & stabile Konstruktion',
                '✓ Zeitloses Design für jeden Wohnstil'
            ],
            'Sofa': [
                '✓ Außergewöhnlicher Sitzkomfort & Entspannung',
                '✓ Strapazierfähige Bezüge & edle Materialien',
                '✓ Stilvolles Design als Wohnraum-Highlight'
            ],
            'Tisch': [
                '✓ Stabile Konstruktion für den täglichen Gebrauch',
                '✓ Vielseitig einsetzbar & funktional',
                '✓ Hochwertige Oberflächen & Materialien'
            ],
            'Regal': [
                '✓ Flexible Aufbewahrung & Präsentation',
                '✓ Stabile Böden für schwere Gegenstände',
                '✓ Modernes Design trifft Funktionalität'
            ],
            'Kommode': [
                '✓ Praktische Aufbewahrung mit Stil',
                '✓ Hochwertige Schubladenauszüge & Beschläge',
                '✓ Elegantes Design für jeden Raum'
            ],
            'Lampe': [
                '✓ Optimale Lichtverteilung & Atmosphäre',
                '✓ Energieeffiziente LED-Technologie',
                '✓ Stilvolles Design als Deko-Element'
            ],
            'Spiegel': [
                '✓ Kristallklare Reflexion & Brillanz',
                '✓ Hochwertige Spiegelbeschichtung',
                '✓ Eleganter Rahmen als Raumschmuck'
            ],
            'Garten': [
                '✓ Wetterfeste Materialien für Outdoor-Einsatz',
                '✓ UV-beständige Oberflächen & Farben',
                '✓ Pflegeleicht & langlebig konstruiert'
            ]
        }
        
        # 通用卖点（作为备选）
        self.generic_selling_points = [
            '✓ Premium-Qualität zum fairen Preis',
            '✓ Schnelle Lieferung & professioneller Service',
            '✓ Zufriedenheitsgarantie & Kundenbetreuung'
        ]
        
    def detect_product_category(self, product_name):
        """检测产品类别"""
        product_name_lower = product_name.lower()
        
        for category, keywords in self.category_keywords.items():
            for keyword in keywords:
                if keyword in product_name_lower:
                    return category
        
        return 'Generic'  # 默认类别
        
    def optimize_description(self, original_description):
        """优化单个产品描述"""
        if pd.isna(original_description) or original_description == '':
            return original_description
        
        desc_str = str(original_description)
        
        # 分离产品信息和卖点
        if '✓' in desc_str:
            parts = desc_str.split('✓', 1)
            product_info = parts[0].strip()
        else:
            product_info = desc_str
        
        # 检测产品类别
        category = self.detect_product_category(product_info)
        
        # 获取优化的卖点
        if category in self.optimized_selling_points:
            selling_points = self.optimized_selling_points[category]
        else:
            selling_points = self.generic_selling_points
        
        # 组合新的描述
        optimized_desc = product_info + ' ' + ' '.join(selling_points)
        
        return optimized_desc
        
    def optimize_csv_file(self, input_file, output_file=None):
        """优化整个CSV文件的短描述"""
        print(f"开始优化文件: {Path(input_file).name}")
        
        try:
            # 读取文件
            df = pd.read_csv(input_file, encoding='utf-8-sig')
            
            print(f"原始数据: {len(df)} 行")
            
            # 统计优化前的情况
            original_descriptions = df['Short description'].dropna()
            print(f"有短描述的产品: {len(original_descriptions)}")
            
            # 优化短描述
            optimized_count = 0
            category_stats = {}
            
            for idx, desc in df['Short description'].items():
                if pd.notna(desc) and desc != '':
                    # 检测类别用于统计
                    if '✓' in str(desc):
                        product_info = str(desc).split('✓', 1)[0].strip()
                        category = self.detect_product_category(product_info)
                        category_stats[category] = category_stats.get(category, 0) + 1
                    
                    # 优化描述
                    optimized_desc = self.optimize_description(desc)
                    df.at[idx, 'Short description'] = optimized_desc
                    optimized_count += 1
                    
                    # 显示进度
                    if optimized_count % 5000 == 0:
                        print(f"已优化 {optimized_count} 个描述...")
            
            print(f"总共优化了 {optimized_count} 个短描述")
            
            # 显示类别统计
            print(f"\\n产品类别分布:")
            for category, count in sorted(category_stats.items(), key=lambda x: x[1], reverse=True):
                percentage = count / optimized_count * 100
                print(f"  {category}: {count} 个 ({percentage:.1f}%)")
            
            # 保存优化后的文件
            if output_file is None:
                output_file = input_file.replace('.csv', '_optimized_descriptions.csv')
            
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            print(f"\\n优化完成，保存到: {output_file}")
            
            return output_file, optimized_count, category_stats
            
        except Exception as e:
            print(f"优化失败: {e}")
            import traceback
            traceback.print_exc()
            return None, 0, {}
            
    def preview_optimization(self, input_file, num_samples=10):
        """预览优化效果"""
        print(f"预览优化效果: {Path(input_file).name}")
        print("="*60)
        
        try:
            df = pd.read_csv(input_file, encoding='utf-8-sig', nrows=100)
            
            descriptions = df['Short description'].dropna().head(num_samples)
            
            for i, desc in enumerate(descriptions, 1):
                print(f"示例 {i}:")
                print(f"原始: {str(desc)[:100]}...")
                
                optimized = self.optimize_description(desc)
                print(f"优化: {optimized[:100]}...")
                
                # 检测类别
                if '✓' in str(desc):
                    product_info = str(desc).split('✓', 1)[0].strip()
                    category = self.detect_product_category(product_info)
                    print(f"类别: {category}")
                
                print("-" * 40)
                
        except Exception as e:
            print(f"预览失败: {e}")

def main():
    """主函数"""
    optimizer = SegmuellerDescriptionOptimizer()
    
    print("🚀 Segmueller产品短描述优化工具")
    print("="*50)
    
    input_file = "woocommerce_output_final/woocommerce_segmueller-de-图片清理小图_final.csv"
    
    if Path(input_file).exists():
        # 先预览优化效果
        print("\\n📋 预览优化效果:")
        optimizer.preview_optimization(input_file, 5)
        
        print("\\n🔧 开始批量优化:")
        output_file, optimized_count, category_stats = optimizer.optimize_csv_file(input_file)
        
        if output_file:
            print(f"\\n✅ 优化成功完成!")
            print(f"优化产品数: {optimized_count:,}")
            print(f"输出文件: {Path(output_file).name}")
        else:
            print("\\n❌ 优化失败")
    else:
        print(f"❌ 文件不存在: {input_file}")

if __name__ == "__main__":
    main()
