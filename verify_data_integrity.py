#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证优化过程中的数据完整性和准确性
"""

import pandas as pd
import numpy as np
from pathlib import Path

def verify_file_integrity(original_file, optimized_file, file_name):
    """验证单个文件的数据完整性"""
    print(f'🔍 验证 {file_name} 数据完整性')
    print('='*50)
    
    try:
        # 读取原始和优化后的文件
        original_df = pd.read_csv(original_file, encoding='utf-8-sig')
        optimized_df = pd.read_csv(optimized_file, encoding='utf-8-sig')
        
        print(f'原始文件: {len(original_df):,} 行, {len(original_df.columns)} 列')
        print(f'优化文件: {len(optimized_df):,} 行, {len(optimized_df.columns)} 列')
        
        # 1. 检查行数是否一致
        if len(original_df) == len(optimized_df):
            print('✅ 行数完全一致 - 无数据丢失')
        else:
            print(f'❌ 行数不一致 - 原始:{len(original_df)}, 优化:{len(optimized_df)}')
            return False
        
        # 2. 检查列数是否一致
        if len(original_df.columns) == len(optimized_df.columns):
            print('✅ 列数完全一致 - 无字段丢失')
        else:
            print(f'❌ 列数不一致 - 原始:{len(original_df.columns)}, 优化:{len(optimized_df.columns)}')
            return False
        
        # 3. 检查列名是否一致
        if list(original_df.columns) == list(optimized_df.columns):
            print('✅ 列名完全一致 - 字段结构保持')
        else:
            print('❌ 列名不一致')
            return False
        
        # 4. 检查关键字段的数据完整性
        key_fields = ['ID', 'SKU', 'Name', 'Regular price', 'Categories', 'Images']
        
        print(f'\n关键字段数据完整性检查:')
        for field in key_fields:
            if field in original_df.columns:
                original_values = original_df[field].fillna('').astype(str)
                optimized_values = optimized_df[field].fillna('').astype(str)
                
                # 检查是否完全相同
                if original_values.equals(optimized_values):
                    print(f'  ✅ {field}: 数据完全一致')
                else:
                    # 计算不同的行数
                    diff_count = (original_values != optimized_values).sum()
                    print(f'  ❌ {field}: {diff_count} 行数据不一致')
                    
                    # 显示前3个不同的示例
                    if diff_count > 0:
                        diff_indices = (original_values != optimized_values)
                        diff_rows = original_df[diff_indices].head(3)
                        
                        print(f'    前3个不同示例:')
                        for idx in diff_rows.index:
                            print(f'      行{idx+1}: 原始="{original_values.iloc[idx][:50]}..."')
                            print(f'             优化="{optimized_values.iloc[idx][:50]}..."')
        
        # 5. 专门检查Short description字段
        print(f'\n📝 Short description 字段变化分析:')
        if 'Short description' in original_df.columns:
            original_short = original_df['Short description'].fillna('')
            optimized_short = optimized_df['Short description'].fillna('')
            
            # 统计变化情况
            changed_count = (original_short != optimized_short).sum()
            unchanged_count = len(original_df) - changed_count
            
            print(f'  变化的描述: {changed_count:,} 个 ({changed_count/len(original_df)*100:.1f}%)')
            print(f'  未变化的描述: {unchanged_count:,} 个 ({unchanged_count/len(original_df)*100:.1f}%)')
            
            # 显示变化示例
            if changed_count > 0:
                print(f'\n  前3个变化示例:')
                changed_indices = (original_short != optimized_short)
                changed_rows = original_df[changed_indices].head(3)
                
                for i, idx in enumerate(changed_rows.index):
                    print(f'    示例{i+1} (行{idx+1}):')
                    print(f'      原始: {original_short.iloc[idx][:80]}...')
                    print(f'      优化: {optimized_short.iloc[idx][:80]}...')
                    print()
        
        # 6. 检查数据类型是否保持
        print(f'\n📊 数据类型一致性检查:')
        type_issues = 0
        for col in original_df.columns:
            if col != 'Short description':  # 短描述预期会变化
                orig_type = original_df[col].dtype
                opt_type = optimized_df[col].dtype
                
                if orig_type != opt_type:
                    print(f'  ⚠️ {col}: 类型变化 {orig_type} -> {opt_type}')
                    type_issues += 1
        
        if type_issues == 0:
            print('  ✅ 所有字段数据类型保持一致')
        
        # 7. 检查空值情况
        print(f'\n🔍 空值情况对比:')
        for field in ['Name', 'SKU', 'Regular price', 'Categories']:
            if field in original_df.columns:
                orig_nulls = original_df[field].isna().sum()
                opt_nulls = optimized_df[field].isna().sum()
                
                if orig_nulls == opt_nulls:
                    print(f'  ✅ {field}: 空值数量一致 ({orig_nulls}个)')
                else:
                    print(f'  ❌ {field}: 空值数量变化 {orig_nulls} -> {opt_nulls}')
        
        print(f'\n🎯 {file_name} 数据完整性验证结果: ✅ 通过')
        return True
        
    except Exception as e:
        print(f'❌ 验证过程出错: {e}')
        import traceback
        traceback.print_exc()
        return False

def verify_optimization_accuracy(original_file, optimized_file, file_name):
    """验证优化的准确性"""
    print(f'\n🎯 验证 {file_name} 优化准确性')
    print('-'*50)
    
    try:
        original_df = pd.read_csv(original_file, encoding='utf-8-sig', nrows=10)
        optimized_df = pd.read_csv(optimized_file, encoding='utf-8-sig', nrows=10)
        
        print('检查优化是否正确应用:')
        
        for i in range(min(5, len(original_df))):
            print(f'\n产品 {i+1}:')
            
            # 检查产品名称是否保持
            orig_name = str(original_df.iloc[i]['Name'])
            opt_name = str(optimized_df.iloc[i]['Name'])
            
            if orig_name == opt_name:
                print(f'  ✅ 产品名称保持一致')
            else:
                print(f'  ❌ 产品名称发生变化')
                print(f'    原始: {orig_name[:50]}...')
                print(f'    优化: {opt_name[:50]}...')
            
            # 检查短描述是否合理优化
            orig_desc = str(original_df.iloc[i]['Short description'])
            opt_desc = str(optimized_df.iloc[i]['Short description'])
            
            # 检查是否包含产品信息
            product_info_preserved = False
            if orig_name[:20] in opt_desc or any(word in opt_desc for word in orig_name.split()[:3]):
                product_info_preserved = True
            
            if product_info_preserved:
                print(f'  ✅ 产品信息在描述中保留')
            else:
                print(f'  ⚠️ 产品信息可能丢失')
            
            # 检查是否包含优化的卖点
            if '✓' in opt_desc and len(opt_desc) > len(orig_desc):
                print(f'  ✅ 卖点优化成功应用')
            else:
                print(f'  ⚠️ 卖点优化可能未正确应用')
        
        return True
        
    except Exception as e:
        print(f'❌ 准确性验证出错: {e}')
        return False

def main():
    """主函数"""
    print('🔍 数据完整性和准确性全面验证')
    print('='*60)
    
    # 定义要验证的文件对
    files_to_verify = [
        {
            'name': 'Segmueller',
            'original': 'woocommerce_output_final/woocommerce_segmueller-de-图片清理小图_final.csv',
            'optimized': 'woocommerce_output_final/woocommerce_segmueller-de-图片清理小图_final_optimized_descriptions.csv'
        },
        {
            'name': 'Klickparts', 
            'original': 'woocommerce_output_final/woocommerce_klickparts-de-3-op_final.csv',
            'optimized': 'woocommerce_output_final/woocommerce_klickparts-de-3-op_final_optimized.csv'
        },
        {
            'name': 'Lampenwelt',
            'original': 'woocommerce_output_final/woocommerce_lampenwelt-de_final.csv', 
            'optimized': 'woocommerce_output_final/woocommerce_lampenwelt-de_final_optimized.csv'
        }
    ]
    
    overall_results = []
    
    for file_info in files_to_verify:
        if Path(file_info['original']).exists() and Path(file_info['optimized']).exists():
            # 验证数据完整性
            integrity_result = verify_file_integrity(
                file_info['original'], 
                file_info['optimized'], 
                file_info['name']
            )
            
            # 验证优化准确性
            accuracy_result = verify_optimization_accuracy(
                file_info['original'],
                file_info['optimized'], 
                file_info['name']
            )
            
            overall_results.append({
                'file': file_info['name'],
                'integrity': integrity_result,
                'accuracy': accuracy_result
            })
            
            print('\n' + '='*60)
        else:
            print(f'❌ {file_info["name"]} 文件不存在')
            overall_results.append({
                'file': file_info['name'],
                'integrity': False,
                'accuracy': False
            })
    
    # 总结报告
    print(f'\n📊 总体验证结果')
    print('='*60)
    
    for result in overall_results:
        integrity_status = '✅ 通过' if result['integrity'] else '❌ 失败'
        accuracy_status = '✅ 通过' if result['accuracy'] else '❌ 失败'
        
        print(f'{result["file"]}:')
        print(f'  数据完整性: {integrity_status}')
        print(f'  优化准确性: {accuracy_status}')
    
    # 总体评估
    all_integrity = all(r['integrity'] for r in overall_results)
    all_accuracy = all(r['accuracy'] for r in overall_results)
    
    print(f'\n🎯 总体评估:')
    if all_integrity and all_accuracy:
        print('✅ 所有文件的数据完整性和优化准确性验证通过')
        print('✅ 优化过程保持了100%的数据完整性')
        print('✅ 优化内容准确应用，无数据损坏或错位')
    else:
        print('⚠️ 部分文件存在问题，需要进一步检查')

if __name__ == "__main__":
    main()
