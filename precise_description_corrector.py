#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确的短描述修正器 - 基于深度产品分析
"""

import pandas as pd
import re
from pathlib import Path

class PreciseDescriptionCorrector:
    def __init__(self):
        """初始化精确修正器"""
        
        # 精确产品识别词典 - 基于实际产品名称分析
        self.precise_product_types = {
            # 宠物用品
            'hundekorb': {
                'type': 'pet_products',
                'sellpoints': [
                    '✓ Gemütlicher Ruheplatz für Ihren Hund',
                    '✓ Strapazierfähige & pflegeleichte Materialien',
                    '✓ Optimale Größe für entspannten Schlaf'
                ]
            },
            'hundekissen': {
                'type': 'pet_products',
                'sellpoints': [
                    '✓ Weiche Liegefläche für maximalen Komfort',
                    '✓ Waschbare Bezüge für einfache Pflege',
                    '✓ Rutschfeste Unterseite für sicheren Halt'
                ]
            },
            
            # 睡眠用品
            'decke': {
                'type': 'bedding',
                'sellpoints': [
                    '✓ Kuschelig weiche Materialien für erholsamen Schlaf',
                    '✓ Temperaturregulierend & atmungsaktiv',
                    '✓ Pflegeleicht & langlebig verarbeitet'
                ]
            },
            'schlafsack': {
                'type': 'sleeping_gear',
                'sellpoints': [
                    '✓ Optimaler Schlafkomfort bei jeder Temperatur',
                    '✓ Kompaktes Packmaß für einfachen Transport',
                    '✓ Strapazierfähige Materialien für Outdoor-Einsatz'
                ]
            },
            'kissen': {
                'type': 'pillows',
                'sellpoints': [
                    '✓ Ergonomische Form für optimalen Liegekomfort',
                    '✓ Hochwertige Füllung für dauerhafte Stützwirkung',
                    '✓ Abnehmbarer Bezug für hygienische Reinigung'
                ]
            },
            'reisekissen': {
                'type': 'travel_pillows',
                'sellpoints': [
                    '✓ Kompakter Reisebegleiter für unterwegs',
                    '✓ Ergonomische Stützung für Nacken & Kopf',
                    '✓ Leichtes Gewicht & platzsparende Aufbewahrung'
                ]
            },
            'schlafmatte': {
                'type': 'sleeping_mats',
                'sellpoints': [
                    '✓ Isolierende Unterlage für warmen Schlaf',
                    '✓ Selbstaufblasend für einfache Handhabung',
                    '✓ Robuste Oberfläche für Outdoor-Bedingungen'
                ]
            },
            
            # 帐篷和遮阳
            'zelt': {
                'type': 'tents',
                'sellpoints': [
                    '✓ Wetterfester Schutz für alle Jahreszeiten',
                    '✓ Einfacher Aufbau mit durchdachtem System',
                    '✓ Hochwertige Materialien für Langlebigkeit'
                ]
            },
            'tunnelzelt': {
                'type': 'tents',
                'sellpoints': [
                    '✓ Geräumiges Tunneldesign für mehr Platz',
                    '✓ Stabile Konstruktion bei Wind & Wetter',
                    '✓ Praktische Aufteilung für optimalen Komfort'
                ]
            },
            'vorzelt': {
                'type': 'awnings',
                'sellpoints': [
                    '✓ Erweitert Ihren Wohnraum im Freien',
                    '✓ Robuste Befestigung am Wohnwagen/Wohnmobil',
                    '✓ Wetterschutz für gesellige Stunden'
                ]
            },
            
            # 厨房用品
            'ofen': {
                'type': 'cooking_appliances',
                'sellpoints': [
                    '✓ Vielseitige Kochmöglichkeiten für unterwegs',
                    '✓ Energieeffiziente Technik & einfache Bedienung',
                    '✓ Kompakte Bauweise für jeden Campingplatz'
                ]
            },
            'grill': {
                'type': 'grilling',
                'sellpoints': [
                    '✓ Perfekte Grillergebnisse für gesellige Abende',
                    '✓ Gleichmäßige Hitzeverteilung & einfache Reinigung',
                    '✓ Robuste Konstruktion für den Outdoor-Einsatz'
                ]
            },
            
            # 户外家具
            'stuhl': {
                'type': 'outdoor_furniture',
                'sellpoints': [
                    '✓ Bequeme Sitzgelegenheit für entspannte Stunden',
                    '✓ Klappbar & leicht für einfachen Transport',
                    '✓ Wetterfeste Materialien für Dauereinsatz'
                ]
            },
            'klappstuhl': {
                'type': 'outdoor_furniture',
                'sellpoints': [
                    '✓ Platzsparend zusammenklappbar',
                    '✓ Leichtes Gewicht für mobilen Einsatz',
                    '✓ Stabile Konstruktion für sicheren Halt'
                ]
            },
            'tisch': {
                'type': 'outdoor_furniture',
                'sellpoints': [
                    '✓ Stabile Arbeitsfläche für vielseitige Nutzung',
                    '✓ Höhenverstellbar für optimalen Komfort',
                    '✓ Witterungsbeständige Oberfläche'
                ]
            },
            'rolltisch': {
                'type': 'outdoor_furniture',
                'sellpoints': [
                    '✓ Praktische Rollfunktion für einfachen Transport',
                    '✓ Schneller Auf- & Abbau ohne Werkzeug',
                    '✓ Stabile Tischplatte für alle Aktivitäten'
                ]
            },
            
            # Bauhaus 产品类型
            'gartenmöbel': {
                'type': 'garden_furniture',
                'sellpoints': [
                    '✓ Stilvolle Gartenausstattung für Wohlfühl-Atmosphäre',
                    '✓ Wetterfeste Materialien für ganzjährigen Einsatz',
                    '✓ Hochwertige Verarbeitung für dauerhafte Freude'
                ]
            },
            'barhocker': {
                'type': 'bar_furniture',
                'sellpoints': [
                    '✓ Moderne Sitzmöbel für Bar & Küche',
                    '✓ Ergonomische Form für bequemes Sitzen',
                    '✓ Stilvolles Design als Wohnraum-Highlight'
                ]
            },
            'überwachungskamera': {
                'type': 'security_equipment',
                'sellpoints': [
                    '✓ Zuverlässige Überwachung für Ihr Zuhause',
                    '✓ HD-Videoqualität & Nachtsichtfunktion',
                    '✓ Einfache Installation & App-Steuerung'
                ]
            },
            'fototapete': {
                'type': 'wall_decoration',
                'sellpoints': [
                    '✓ Beeindruckende Wandgestaltung mit fotorealistischen Motiven',
                    '✓ Hochauflösende Druckqualität für brillante Farben',
                    '✓ Einfache Anbringung für schnelle Raumverwandlung'
                ]
            },
            'fensterbank': {
                'type': 'building_materials',
                'sellpoints': [
                    '✓ Hochwertige Fensterbank für stilvolle Raumgestaltung',
                    '✓ Pflegeleichte Oberfläche & dauerhafte Haltbarkeit',
                    '✓ Präzise Maße für perfekte Passgenauigkeit'
                ]
            },
            'betonmischer': {
                'type': 'construction_equipment',
                'sellpoints': [
                    '✓ Leistungsstarke Mischtechnik für professionelle Ergebnisse',
                    '✓ Robuste Bauweise für den harten Baustelleneinsatz',
                    '✓ Einfache Bedienung & wartungsfreundliche Konstruktion'
                ]
            },
            'hobelmaschine': {
                'type': 'woodworking_tools',
                'sellpoints': [
                    '✓ Präzise Holzbearbeitung für perfekte Oberflächen',
                    '✓ Leistungsstarker Motor für effizientes Arbeiten',
                    '✓ Staubabsaugung für sauberen Arbeitsplatz'
                ]
            },
            'deckenleuchte': {
                'type': 'lighting',
                'sellpoints': [
                    '✓ Moderne Beleuchtung für optimale Raumausleuchtung',
                    '✓ Energieeffiziente LED-Technologie',
                    '✓ Stilvolles Design als dekorativer Raumschmuck'
                ]
            }
        }
        
        # 通用卖点 (作为备选)
        self.generic_sellpoints = {
            'obelink': [
                '✓ Hochwertige Outdoor-Ausrüstung für jedes Abenteuer',
                '✓ Funktionales Design & zuverlässige Qualität',
                '✓ Optimiert für Camping & Freizeitaktivitäten'
            ],
            'bauhaus': [
                '✓ Professionelle Bauhaus-Qualität für Ihr Projekt',
                '✓ Langlebige Materialien & präzise Verarbeitung',
                '✓ Funktionale Lösung für anspruchsvolle Anwendungen'
            ]
        }
        
    def detect_precise_product_type(self, product_name, category=''):
        """精确检测产品类型"""
        name_lower = str(product_name).lower()
        category_lower = str(category).lower()
        
        # 按优先级检测 - 从最具体到最通用
        for keyword, config in self.precise_product_types.items():
            if keyword in name_lower:
                return config
        
        # 如果没有精确匹配，尝试分类匹配
        if 'hund' in name_lower or 'tier' in category_lower:
            return self.precise_product_types.get('hundekorb')
        elif 'schlafen' in category_lower and 'decke' in name_lower:
            return self.precise_product_types.get('decke')
        elif 'zelt' in category_lower:
            return self.precise_product_types.get('zelt')
        elif 'stuhl' in category_lower or 'möbel' in category_lower:
            if 'bar' in name_lower:
                return self.precise_product_types.get('barhocker')
            else:
                return self.precise_product_types.get('stuhl')
        
        return None
        
    def generate_corrected_description(self, product_name, category, store_type):
        """生成修正后的短描述"""
        if pd.isna(product_name) or product_name == '':
            return ''
        
        # 清理产品名称
        clean_name = str(product_name).strip()
        if len(clean_name) > 50:
            # 保留前50个字符，在合适位置截断
            clean_name = clean_name[:50]
            if ' ' in clean_name:
                clean_name = clean_name.rsplit(' ', 1)[0]
        
        # 精确检测产品类型
        product_config = self.detect_precise_product_type(product_name, category)
        
        if product_config:
            # 使用精确匹配的卖点
            sellpoints = product_config['sellpoints']
        else:
            # 使用通用卖点
            sellpoints = self.generic_sellpoints.get(store_type, self.generic_sellpoints['obelink'])
        
        # 组合短描述
        corrected_desc = clean_name + ' ' + ' '.join(sellpoints)
        
        return corrected_desc
        
    def correct_file(self, file_path, store_type):
        """修正整个文件的短描述"""
        file_name = Path(file_path).name
        print(f"开始修正 {file_name}")
        
        try:
            df = pd.read_csv(file_path, encoding='utf-8-sig')
            print(f"原始数据: {len(df):,} 行")
            
            corrected_count = 0
            type_stats = {}
            
            for idx, row in df.iterrows():
                name = row['Name']
                category = row.get('Categories', '')
                
                # 生成修正后的短描述
                corrected_desc = self.generate_corrected_description(name, category, store_type)
                df.at[idx, 'Short description'] = corrected_desc
                
                if corrected_desc:
                    corrected_count += 1
                    
                    # 统计产品类型
                    product_config = self.detect_precise_product_type(name, category)
                    if product_config:
                        product_type = product_config['type']
                    else:
                        product_type = 'generic'
                    
                    type_stats[product_type] = type_stats.get(product_type, 0) + 1
                
                # 显示进度
                if (idx + 1) % 5000 == 0:
                    print(f"已处理 {idx + 1:,} 个产品...")
            
            print(f"短描述修正完成: {corrected_count:,} 个产品")
            
            # 显示产品类型统计
            print(f"\n产品类型分布:")
            for ptype, count in sorted(type_stats.items(), key=lambda x: x[1], reverse=True):
                percentage = count / corrected_count * 100 if corrected_count > 0 else 0
                print(f"  {ptype}: {count:,} 个 ({percentage:.1f}%)")
            
            # 保存修正后的文件
            output_file = file_path.replace('_with_short_desc.csv', '_corrected_desc.csv')
            if output_file == file_path:
                output_file = file_path.replace('.csv', '_corrected_desc.csv')
            
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            print(f"修正完成，保存到: {Path(output_file).name}")
            
            return output_file, corrected_count, type_stats
            
        except Exception as e:
            print(f"修正失败: {e}")
            import traceback
            traceback.print_exc()
            return None, 0, {}
            
    def preview_corrections(self, file_path, store_type, num_samples=10):
        """预览修正效果"""
        print(f"预览 {store_type.title()} 修正效果:")
        print("="*70)
        
        try:
            # 读取原始文件和当前有问题的文件
            if '_with_short_desc.csv' in file_path:
                original_file = file_path.replace('_with_short_desc.csv', '.csv')
            else:
                original_file = file_path
                
            df_current = pd.read_csv(file_path, encoding='utf-8-sig', nrows=30)
            
            problem_cases = []
            
            # 找出有问题的案例
            for i, row in df_current.iterrows():
                name = str(row['Name']).lower()
                current_desc = str(row['Short description']).lower()
                
                # 识别明显错误的案例
                is_problem = False
                problem_type = ""
                
                if 'hundekorb' in name and 'küchenhelfer' in current_desc:
                    is_problem = True
                    problem_type = "狗篮子→厨房用品"
                elif 'überwachungskamera' in name and 'infrarot' in current_desc:
                    is_problem = True
                    problem_type = "监控摄像头→红外加热"
                elif 'barhocker' in name and 'garten' in current_desc:
                    is_problem = True
                    problem_type = "吧台椅→花园用品"
                elif 'fensterbank' in name and 'bauhaus-qualität' in current_desc:
                    is_problem = True
                    problem_type = "窗台→通用卖点"
                elif 'decke' in name and 'küche' in current_desc:
                    is_problem = True
                    problem_type = "毯子→厨房用品"
                
                if is_problem:
                    problem_cases.append({
                        'index': i,
                        'name': row['Name'],
                        'current_desc': row['Short description'],
                        'problem_type': problem_type
                    })
                    
                    if len(problem_cases) >= num_samples:
                        break
            
            # 显示修正对比
            for i, case in enumerate(problem_cases, 1):
                print(f"问题案例 {i}: {case['problem_type']}")
                print(f"产品名称: {case['name'][:50]}...")
                print(f"当前错误: {case['current_desc'][:60]}...")
                
                # 生成修正版本
                corrected_desc = self.generate_corrected_description(
                    case['name'], 
                    df_current.iloc[case['index']].get('Categories', ''), 
                    store_type
                )
                print(f"修正版本: {corrected_desc[:60]}...")
                
                # 评估修正效果
                name_lower = case['name'].lower()
                corrected_lower = corrected_desc.lower()
                
                relevance_improved = False
                if 'hundekorb' in name_lower and ('hund' in corrected_lower or 'ruheplatz' in corrected_lower):
                    relevance_improved = True
                elif 'überwachungskamera' in name_lower and 'überwachung' in corrected_lower:
                    relevance_improved = True
                elif 'barhocker' in name_lower and ('sitz' in corrected_lower or 'bar' in corrected_lower):
                    relevance_improved = True
                elif 'decke' in name_lower and 'schlaf' in corrected_lower:
                    relevance_improved = True
                
                if relevance_improved:
                    print("✅ 相关性显著改善")
                else:
                    print("⚠️ 需要进一步优化")
                
                print("-" * 70)
                
        except Exception as e:
            print(f"预览失败: {e}")

def main():
    """主函数"""
    corrector = PreciseDescriptionCorrector()
    
    print("🔧 精确短描述修正器")
    print("="*60)
    print("修正策略:")
    print("• 基于精确产品名称关键词识别")
    print("• 为每种产品类型定制专门卖点")
    print("• 消除明显的产品类型错误匹配")
    print("• 提升短描述与产品的相关性")
    print("="*60)
    
    files_to_correct = [
        ('woocommerce_output_final/woocommerce_obelink-de_final_with_short_desc.csv', 'obelink'),
        ('woocommerce_output_final/woocommerce_bauhaus-at-de-图片前两图_final_with_short_desc.csv', 'bauhaus')
    ]
    
    total_corrected = 0
    
    for file_path, store_type in files_to_correct:
        if Path(file_path).exists():
            print(f"\n📋 预览 {store_type.title()} 修正效果:")
            corrector.preview_corrections(file_path, store_type, 5)
            
            print(f"\n🔧 开始修正 {store_type.title()}:")
            output_file, corrected_count, type_stats = corrector.correct_file(file_path, store_type)
            
            if output_file:
                print(f"✅ {store_type.title()} 修正成功!")
                total_corrected += corrected_count
            else:
                print(f"❌ {store_type.title()} 修正失败")
        else:
            print(f"❌ 文件不存在: {file_path}")
        
        print("\n" + "="*60)
    
    print(f"\n📊 修正总结")
    print("="*60)
    print(f"总修正产品数: {total_corrected:,}")
    print("修正效果:")
    print("✅ 消除了明显的产品类型错误匹配")
    print("✅ 为每种产品提供了相关性更高的卖点")
    print("✅ 保持了德语表达的自然性")
    print("✅ 提升了整体短描述质量")

if __name__ == "__main__":
    main()
