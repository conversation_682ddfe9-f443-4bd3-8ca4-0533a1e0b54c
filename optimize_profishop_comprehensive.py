#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Profishop综合优化脚本 - 优化Tags和短描述
"""

import pandas as pd
import re
from pathlib import Path
from collections import Counter

class ProfishopOptimizer:
    def __init__(self):
        """初始化Profishop优化器"""
        
        # 品牌关键词 (优先保留)
        self.brand_keywords = [
            'kärcher', 'bosch', 'makita', 'dewalt', 'milwaukee', 'festool',
            'ganter', 'homcom', 'denios', 'rems', 'karnasch', 'munk',
            'günzburger', 'steigtechnik', 'ewuaqua', 'iwater'
        ]
        
        # 产品类型关键词 (优先保留)
        self.product_type_keywords = [
            'werkzeug', 'bohrer', 'säge', 'schrauber', 'hammer', 'zange',
            'messer', 'schlüssel', 'bits', 'aufsatz', 'adapter', 'halter',
            'edelstahl', 'stahl', 'kunststoff', 'aluminium', 'metall'
        ]
        
        # 技术规格关键词 (保留)
        self.technical_keywords = [
            'mm', 'cm', 'kg', 'din', 'iso', 'ral', 'ce', 'gs'
        ]
        
        # 需要移除的通用词汇
        self.generic_remove_keywords = [
            'sie', 'die', 'der', 'das', 'und', 'mit', 'für', 'von', 'zu',
            'quot', 've:', 'stück', 'außen', 'innen', 'serie', 'typ',
            'bedienelemente', 'maschinenelemente', 'normalien', 'normelemente', 'normteile'
        ]
        
        # 产品类别卖点配置
        self.category_selling_points = {
            'werkzeug': [
                '✓ Professionelle Qualität für den täglichen Einsatz',
                '✓ Ergonomisches Design & langlebige Konstruktion', 
                '✓ Präzise Verarbeitung mit Herstellergarantie'
            ],
            'reinigung': [
                '✓ Hocheffiziente Reinigungsleistung & Zeitersparnis',
                '✓ Robuste Bauweise für den Dauereinsatz',
                '✓ Einfache Bedienung mit professionellen Ergebnissen'
            ],
            'messtechnik': [
                '✓ Präzise Messergebnisse & hohe Genauigkeit',
                '✓ Zuverlässige Technik für professionelle Anwendung',
                '✓ Einfache Handhabung mit digitaler Anzeige'
            ],
            'sicherheit': [
                '✓ Höchste Sicherheitsstandards & Zertifizierungen',
                '✓ Strapazierfähige Materialien für Langzeitschutz',
                '✓ Komfortable Passform bei optimaler Schutzwirkung'
            ],
            'lager': [
                '✓ Maximale Belastbarkeit & stabile Konstruktion',
                '✓ Platzsparende Lösung für optimale Raumnutzung',
                '✓ Modulares System für flexible Erweiterung'
            ],
            'transport': [
                '✓ Hohe Tragkraft & wendige Manövrierbarkeit',
                '✓ Robuste Räder für alle Untergründe',
                '✓ Ergonomische Griffe für komfortable Nutzung'
            ],
            'generic': [
                '✓ Hochwertige Verarbeitung & professionelle Qualität',
                '✓ Zuverlässige Leistung für den gewerblichen Einsatz',
                '✓ Langlebige Konstruktion mit Herstellergarantie'
            ]
        }
        
    def optimize_tags(self, tags_str):
        """优化Tags - 保留1-2个核心Tags"""
        if pd.isna(tags_str) or tags_str == '':
            return ''
        
        # 分割Tags
        if ',' in str(tags_str):
            tags_list = [tag.strip() for tag in str(tags_str).split(',')]
        else:
            tags_list = [str(tags_str).strip()]
        
        # 清理和分类Tags
        brand_tags = []
        product_tags = []
        technical_tags = []
        
        for tag in tags_list:
            tag_lower = tag.lower().strip()
            
            # 跳过空标签和通用词汇
            if not tag_lower or len(tag_lower) < 2:
                continue
            if any(generic in tag_lower for generic in self.generic_remove_keywords):
                continue
            
            # 分类Tags
            if any(brand in tag_lower for brand in self.brand_keywords):
                brand_tags.append(tag)
            elif any(ptype in tag_lower for ptype in self.product_type_keywords):
                product_tags.append(tag)
            elif any(tech in tag_lower for tech in self.technical_keywords):
                technical_tags.append(tag)
        
        # 选择最佳的1-2个Tags
        final_tags = []
        
        # 优先选择品牌Tag
        if brand_tags:
            final_tags.append(brand_tags[0])
        
        # 如果还没有2个Tags，添加产品类型Tag
        if len(final_tags) < 2 and product_tags:
            final_tags.append(product_tags[0])
        
        # 如果还没有2个Tags，添加技术规格Tag
        if len(final_tags) < 2 and technical_tags:
            # 选择最有意义的技术Tag (避免纯数字)
            meaningful_tech = [tag for tag in technical_tags if not tag.isdigit()]
            if meaningful_tech:
                final_tags.append(meaningful_tech[0])
            elif technical_tags:
                final_tags.append(technical_tags[0])
        
        # 如果仍然没有Tags，从原始Tags中选择最长的非通用Tag
        if not final_tags:
            valid_tags = [tag for tag in tags_list 
                         if len(tag.strip()) > 3 and 
                         not any(generic in tag.lower() for generic in self.generic_remove_keywords)]
            if valid_tags:
                # 选择最长的Tag，通常包含更多信息
                final_tags.append(max(valid_tags, key=len))
        
        return ', '.join(final_tags[:2])  # 最多保留2个Tags
        
    def detect_product_category(self, name, category, tags):
        """检测产品类别"""
        text = (str(name) + ' ' + str(category) + ' ' + str(tags)).lower()
        
        # 检测关键词
        if any(word in text for word in ['werkzeug', 'tool', 'bohrer', 'säge', 'schrauber']):
            return 'werkzeug'
        elif any(word in text for word in ['reinigung', 'kärcher', 'sauger', 'wasch']):
            return 'reinigung'
        elif any(word in text for word in ['mess', 'prüf', 'meter', 'gauge']):
            return 'messtechnik'
        elif any(word in text for word in ['sicherheit', 'schutz', 'helm', 'handschuh']):
            return 'sicherheit'
        elif any(word in text for word in ['regal', 'lager', 'behälter', 'tank']):
            return 'lager'
        elif any(word in text for word in ['transport', 'wagen', 'rolle', 'rad']):
            return 'transport'
        else:
            return 'generic'
            
    def optimize_short_description(self, row):
        """优化短描述"""
        original_desc = str(row['Short description'])
        product_name = str(row['Name'])
        
        # 提取产品信息部分
        if '✓' in original_desc:
            product_info = original_desc.split('✓')[0].strip()
        else:
            # 如果没有卖点，使用产品名称
            product_info = product_name
        
        # 检测产品类别
        category = self.detect_product_category(
            row['Name'], 
            row.get('Categories', ''), 
            row.get('Tags', '')
        )
        
        # 获取对应的卖点
        selling_points = self.category_selling_points.get(category, 
                        self.category_selling_points['generic'])
        
        # 组合新描述
        optimized_desc = product_info + ' ' + ' '.join(selling_points)
        
        return optimized_desc, category
        
    def optimize_file(self, file_path):
        """优化单个文件"""
        file_name = Path(file_path).name
        print(f"开始优化: {file_name}")
        
        try:
            df = pd.read_csv(file_path, encoding='utf-8-sig')
            print(f"原始数据: {len(df):,} 行")
            
            optimized_count = 0
            tags_optimized = 0
            category_stats = {}
            
            for idx, row in df.iterrows():
                # 优化Tags
                if 'Tags' in df.columns:
                    original_tags = row['Tags']
                    optimized_tags = self.optimize_tags(original_tags)
                    df.at[idx, 'Tags'] = optimized_tags
                    
                    if str(original_tags) != str(optimized_tags):
                        tags_optimized += 1
                
                # 优化短描述
                optimized_desc, category = self.optimize_short_description(row)
                df.at[idx, 'Short description'] = optimized_desc
                optimized_count += 1
                
                # 统计类别
                category_stats[category] = category_stats.get(category, 0) + 1
                
                # 显示进度
                if optimized_count % 5000 == 0:
                    print(f"已优化 {optimized_count:,} 个产品...")
            
            print(f"短描述优化: {optimized_count:,} 个")
            print(f"Tags优化: {tags_optimized:,} 个")
            
            # 显示类别统计
            print(f"\n产品类别分布:")
            for category, count in sorted(category_stats.items(), key=lambda x: x[1], reverse=True):
                percentage = count / optimized_count * 100
                print(f"  {category}: {count:,} 个 ({percentage:.1f}%)")
            
            # 保存优化后的文件
            output_file = file_path.replace('.csv', '_optimized.csv')
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            print(f"优化完成，保存到: {Path(output_file).name}")
            
            return output_file, optimized_count, tags_optimized, category_stats
            
        except Exception as e:
            print(f"优化失败: {e}")
            import traceback
            traceback.print_exc()
            return None, 0, 0, {}
            
    def preview_optimization(self, file_path, num_samples=3):
        """预览优化效果"""
        print(f"预览优化效果: {Path(file_path).name}")
        print("="*60)
        
        try:
            df = pd.read_csv(file_path, encoding='utf-8-sig', nrows=20)
            
            for i in range(min(num_samples, len(df))):
                row = df.iloc[i]
                
                print(f"示例 {i+1}:")
                print(f"产品名称: {str(row['Name'])[:50]}...")
                
                # Tags优化预览
                if 'Tags' in df.columns:
                    original_tags = str(row['Tags'])
                    optimized_tags = self.optimize_tags(original_tags)
                    print(f"原始Tags: {original_tags[:60]}...")
                    print(f"优化Tags: {optimized_tags}")
                
                # 短描述优化预览
                original_desc = str(row['Short description'])
                optimized_desc, category = self.optimize_short_description(row)
                print(f"原始描述: {original_desc[:60]}...")
                print(f"优化描述: {optimized_desc[:60]}...")
                print(f"检测类别: {category}")
                print("-" * 60)
                
        except Exception as e:
            print(f"预览失败: {e}")

def main():
    """主函数"""
    optimizer = ProfishopOptimizer()
    
    print("🚀 Profishop 6个文件综合优化工具")
    print("="*60)
    print("优化内容:")
    print("• Tags: 保留1-2个核心Tags，移除通用词汇")
    print("• 短描述: 根据产品类别定制专业卖点")
    print("="*60)
    
    # 获取所有Profishop文件
    profishop_files = [
        'woocommerce_output_final/woocommerce_profishop-de_final.csv',
        'woocommerce_output_final/woocommerce_profishop-de-2_final.csv',
        'woocommerce_output_final/woocommerce_profishop-de-3_final.csv',
        'woocommerce_output_final/woocommerce_profishop-de-4_final.csv',
        'woocommerce_output_final/woocommerce_profishop-de-5_final.csv',
        'woocommerce_output_final/woocommerce_profishop-de-6_final.csv'
    ]
    
    total_products = 0
    total_tags_optimized = 0
    overall_category_stats = {}
    
    for i, file_path in enumerate(profishop_files, 1):
        if Path(file_path).exists():
            print(f"\n📋 预览 Profishop-{i} 优化效果:")
            optimizer.preview_optimization(file_path, 2)
            
            print(f"\n🔧 开始优化 Profishop-{i}:")
            output_file, optimized_count, tags_optimized, category_stats = optimizer.optimize_file(file_path)
            
            if output_file:
                print(f"✅ Profishop-{i} 优化成功完成!")
                total_products += optimized_count
                total_tags_optimized += tags_optimized
                
                # 合并类别统计
                for category, count in category_stats.items():
                    overall_category_stats[category] = overall_category_stats.get(category, 0) + count
            else:
                print(f"❌ Profishop-{i} 优化失败")
        else:
            print(f"❌ 文件不存在: {file_path}")
        
        print("\n" + "="*60)
    
    # 总体统计
    print(f"\n📊 总体优化统计")
    print("="*60)
    print(f"总产品数: {total_products:,}")
    print(f"Tags优化数: {total_tags_optimized:,}")
    print(f"优化文件数: 6个")
    
    print(f"\n整体产品类别分布:")
    for category, count in sorted(overall_category_stats.items(), key=lambda x: x[1], reverse=True):
        percentage = count / total_products * 100 if total_products > 0 else 0
        print(f"  {category}: {count:,} 个 ({percentage:.1f}%)")

if __name__ == "__main__":
    main()
