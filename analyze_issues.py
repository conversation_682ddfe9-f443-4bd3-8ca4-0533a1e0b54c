#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析转换问题脚本
"""

import pandas as pd
import numpy as np
from pathlib import Path

def analyze_description_issue():
    """分析描述字段问题"""
    print('=== 问题5&8：描述字段分析 ===')
    
    # 检查klickparts文件
    source_file = '源数据文件/de/klickparts-de-3-op.xlsx'
    output_file = 'woocommerce_output_optimized/woocommerce_klickparts-de-3-op_optimized.csv'
    
    if Path(source_file).exists() and Path(output_file).exists():
        source_df = pd.read_excel(source_file, nrows=5)
        output_df = pd.read_csv(output_file, encoding='utf-8-sig', nrows=5)
        
        print('klickparts源文件列名:', list(source_df.columns))
        print()
        
        for i in range(3):
            print(f'第{i+1}行:')
            if 'short description' in source_df.columns:
                short_desc = str(source_df.iloc[i]['short description'])
                print(f'  源短描述: {short_desc[:100]}...')
            if 'description' in source_df.columns:
                long_desc = str(source_df.iloc[i]['description'])
                print(f'  源长描述: {long_desc[:100]}...')
            
            output_short = str(output_df.iloc[i]['Short description'])
            output_long = str(output_df.iloc[i]['Description'])
            print(f'  输出短描述: {output_short[:100]}...')
            print(f'  输出长描述: {output_long[:100]}...')
            print()
    
    # 检查obelink文件
    print('=== obelink描述检查 ===')
    source_file2 = '源数据文件/de/obelink-de.xlsx'
    output_file2 = 'woocommerce_output_optimized/woocommerce_obelink-de_optimized.csv'
    
    if Path(source_file2).exists() and Path(output_file2).exists():
        source_df2 = pd.read_excel(source_file2, nrows=5)
        output_df2 = pd.read_csv(output_file2, encoding='utf-8-sig', nrows=5)
        
        print('obelink源文件列名:', list(source_df2.columns))
        print()
        
        for i in range(3):
            print(f'第{i+1}行:')
            if 'Short description' in source_df2.columns:
                short_desc = str(source_df2.iloc[i]['Short description'])
                print(f'  源短描述: {short_desc[:100]}...')
            if 'Description' in source_df2.columns:
                long_desc = str(source_df2.iloc[i]['Description'])
                print(f'  源长描述: {long_desc[:100]}...')
            
            output_short = str(output_df2.iloc[i]['Short description'])
            output_long = str(output_df2.iloc[i]['Description'])
            print(f'  输出短描述: {output_short[:100]}...')
            print(f'  输出长描述: {output_long[:100]}...')
            print()

def analyze_attribute_issues():
    """分析属性问题"""
    print('=== 问题1&2：属性设置检查 ===')
    
    output_file = 'woocommerce_output_optimized/woocommerce_bauhaus-at-de-图片前两图_optimized.csv'
    if Path(output_file).exists():
        df = pd.read_csv(output_file, encoding='utf-8-sig', nrows=10)
        
        print('属性列检查:')
        attr_cols = ['Attribute 1 name', 'Attribute 1 value(s)', 'Attribute 1 visible', 'Attribute 1 global']
        for col in attr_cols:
            if col in df.columns:
                print(f'{col}: {list(df[col].head(5))}')
        
        # 检查空属性的设置
        empty_attrs = df[df['Attribute 1 name'].isna() | (df['Attribute 1 name'] == '')]
        if len(empty_attrs) > 0:
            print(f'\\n空属性行数: {len(empty_attrs)}')
            print('空属性的visible和global设置:')
            print(empty_attrs[['Attribute 1 visible', 'Attribute 1 global']].head())

def analyze_upc_issue():
    """分析UPC/EAN问题"""
    print('=== 问题6：UPC/EAN属性检查 ===')
    
    # 检查有UPC字段的源文件
    source_file = '源数据文件/de/bloomled-de-op.xlsx'
    output_file = 'woocommerce_output_optimized/woocommerce_bloomled-de-op_optimized.csv'
    
    if Path(source_file).exists() and Path(output_file).exists():
        source_df = pd.read_excel(source_file, nrows=5)
        output_df = pd.read_csv(output_file, encoding='utf-8-sig', nrows=5)
        
        print('bloomled源文件列名:', list(source_df.columns))
        
        if 'UPC' in source_df.columns:
            print('\\n源文件UPC示例:')
            for i in range(3):
                upc_val = str(source_df.iloc[i]['UPC'])
                print(f'  第{i+1}行UPC: {upc_val}')
        
        print('\\n输出文件属性列:')
        attr_cols = [col for col in output_df.columns if 'Attribute' in col]
        for col in attr_cols:
            print(f'{col}: {list(output_df[col].head(3))}')

def analyze_category_issue():
    """分析分类问题"""
    print('=== 问题7：分类逗号检查 ===')
    
    output_file = 'woocommerce_output_optimized/woocommerce_bauhaus-at-de-图片前两图_optimized.csv'
    if Path(output_file).exists():
        df = pd.read_csv(output_file, encoding='utf-8-sig', nrows=100)
        
        categories = df['Categories'].dropna()
        comma_categories = categories[categories.str.contains(',', na=False)]
        
        print(f'包含逗号的分类数量: {len(comma_categories)}')
        if len(comma_categories) > 0:
            print('示例:')
            for i, cat in enumerate(comma_categories.head(5)):
                print(f'  {i+1}. {cat}')

def analyze_image_issue():
    """分析图片数量问题"""
    print('=== 问题9：图片数量检查 ===')
    
    output_file = 'woocommerce_output_optimized/woocommerce_bauhaus-at-de-图片前两图_optimized.csv'
    if Path(output_file).exists():
        df = pd.read_csv(output_file, encoding='utf-8-sig', nrows=20)
        
        images = df['Images'].dropna()
        for i, img in enumerate(images.head(5)):
            img_count = len(img.split(',')) if img else 0
            print(f'第{i+1}行图片数量: {img_count}')
            if img_count > 5:
                print(f'  超过5张: {img}')

def analyze_stock_and_id():
    """分析库存和ID问题"""
    print('=== 问题3&4：库存和ID检查 ===')
    
    output_file = 'woocommerce_output_optimized/woocommerce_bauhaus-at-de-图片前两图_optimized.csv'
    if Path(output_file).exists():
        df = pd.read_csv(output_file, encoding='utf-8-sig', nrows=10)
        
        print('ID列内容:', list(df['ID'].head(5)))
        print('Stock列内容:', list(df['Stock'].head(5)))
        
        # 检查库存相关字段
        stock_cols = ['Stock', 'In stock?', 'Low stock amount']
        for col in stock_cols:
            if col in df.columns:
                print(f'{col}: {list(df[col].head(5))}')

def main():
    """主函数"""
    print('🔍 转换问题详细分析')
    print('='*50)
    
    analyze_description_issue()
    print('\\n' + '='*50)
    analyze_attribute_issues()
    print('\\n' + '='*50)
    analyze_upc_issue()
    print('\\n' + '='*50)
    analyze_category_issue()
    print('\\n' + '='*50)
    analyze_image_issue()
    print('\\n' + '='*50)
    analyze_stock_and_id()

if __name__ == "__main__":
    main()
