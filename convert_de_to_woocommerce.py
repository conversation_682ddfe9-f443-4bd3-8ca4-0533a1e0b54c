#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
德国数据源专用WooCommerce转换脚本
优化支持德语内容、欧元价格格式和德国电商数据结构
"""

import pandas as pd
import numpy as np
from pathlib import Path
import re
import sys
import html
from urllib.parse import urlparse
import logging
from datetime import datetime

class GermanWooCommerceConverter:
    def __init__(self):
        """初始化德国数据转换器"""
        self.setup_logging()
        
        # 德语字段映射
        self.field_mappings = {
            'name_fields': ['title', 'Name', 'product_name', 'titel', 'produktname'],
            'price_fields': ['price', 'regular_price', 'preis', 'cost', 'grundpreis'],
            'sale_price_fields': ['sale_price', 'angebotspreis', 'aktionspreis', 'reduziert'],
            'description_fields': ['description', 'beschreibung', 'detail', 'produktbeschreibung'],
            'short_desc_fields': ['short description', 'short description ', 'kurzbeschreibung', 'zusammenfassung'],
            'category_fields': ['category', 'kategorie', 'cate', 'produktkategorie'],
            'image_fields': ['image', 'images', 'image0', 'bild', 'bilder', 'foto'],
            'brand_fields': ['Brand', 'Marke', 'Hersteller', 'MFG', 'brand'],
            'tags_fields': ['tags', 'schlagwörter', 'keywords', 'stichwörter'],
            'sku_fields': ['SKU', 'sku', 'ID', 'product_id', 'artikelnummer'],
            'upc_fields': ['UPC', 'EAN', 'GTIN', 'barcode']
        }
        
        # WooCommerce标准字段
        self.wc_fields = [
            'ID', 'Type', 'SKU', 'Name', 'Published', 'Is featured?', 'Visibility in catalog',
            'Short description', 'Description', 'Date sale price starts', 'Date sale price ends',
            'Tax status', 'Tax class', 'In stock?', 'Stock', 'Low stock amount', 'Backorders allowed?',
            'Sold individually?', 'Weight (kg)', 'Length (cm)', 'Width (cm)', 'Height (cm)',
            'Allow customer reviews?', 'Purchase note', 'Sale price', 'Regular price', 'Categories',
            'Tags', 'Shipping class', 'Images', 'Download limit', 'Download expiry days', 'Parent',
            'Grouped products', 'Upsells', 'Cross-sells', 'External URL', 'Button text', 'Position',
            'Attribute 1 name', 'Attribute 1 value(s)', 'Attribute 1 visible', 'Attribute 1 global'
        ]
        
        # 德语分类映射
        self.category_translations = {
            'Farben & Tapeten': 'Home & Garden > Paint & Wallpaper',
            'Tapeten': 'Home & Garden > Wallpaper',
            'Fototapeten': 'Home & Garden > Photo Wallpaper',
            'Möbel': 'Home & Garden > Furniture',
            'Beleuchtung': 'Home & Garden > Lighting',
            'Lampen': 'Home & Garden > Lamps',
            'Werkzeuge': 'Tools & Hardware',
            'Garten': 'Home & Garden > Outdoor',
            'Küche': 'Home & Garden > Kitchen',
            'Bad': 'Home & Garden > Bathroom'
        }
        
    def setup_logging(self):
        """设置日志记录"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('de_conversion.log', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def detect_field(self, df, field_list):
        """智能检测字段"""
        for field in field_list:
            for col in df.columns:
                if field.lower() in col.lower() or col.lower() in field.lower():
                    return col
        return None
        
    def clean_german_price(self, price_str):
        """清理德语价格格式"""
        if pd.isna(price_str) or price_str == '':
            return 0
            
        price_str = str(price_str).strip()
        
        # 移除货币符号和空格
        price_str = re.sub(r'[€$£¥\s]', '', price_str)
        
        # 处理德语数字格式 (1.234,56 -> 1234.56)
        if ',' in price_str and '.' in price_str:
            # 如果同时有逗号和点，逗号是小数分隔符
            price_str = price_str.replace('.', '').replace(',', '.')
        elif ',' in price_str:
            # 只有逗号，判断是否为小数分隔符
            parts = price_str.split(',')
            if len(parts) == 2 and len(parts[1]) <= 2:
                price_str = price_str.replace(',', '.')
            else:
                price_str = price_str.replace(',', '')
                
        try:
            return float(price_str)
        except ValueError:
            return 0
            
    def clean_german_html(self, html_content):
        """清理德语HTML内容"""
        if pd.isna(html_content) or html_content == '':
            return ''

        content = str(html_content)

        # 移除危险标签
        dangerous_tags = ['script', 'style', 'iframe', 'object', 'embed']
        for tag in dangerous_tags:
            content = re.sub(f'<{tag}[^>]*>.*?</{tag}>', '', content, flags=re.DOTALL | re.IGNORECASE)

        # 更精确地移除事件处理器 - 只匹配HTML属性中的事件处理器
        # 使用更严格的模式，避免误匹配德语单词
        event_patterns = [
            r'\s+on(click|load|mouse\w+|key\w+|focus|blur|change|submit)\s*=\s*["\'][^"\']*["\']',
            r'\s+on(click|load|mouse\w+|key\w+|focus|blur|change|submit)\s*=\s*[^>\s]+',
        ]
        for pattern in event_patterns:
            content = re.sub(pattern, '', content, flags=re.IGNORECASE)

        # 清理多余空白但保留德语字符
        content = re.sub(r'\s+', ' ', content).strip()

        # 确保HTML实体正确编码
        content = html.unescape(content)

        return content
        
    def translate_category(self, german_category):
        """翻译德语分类"""
        if pd.isna(german_category) or german_category == '':
            return 'General'
            
        category = str(german_category).strip()
        
        # 直接映射
        for de_cat, en_cat in self.category_translations.items():
            if de_cat in category:
                return en_cat
                
        # 保持层级结构，只翻译关键词
        parts = category.split('>')
        translated_parts = []
        
        for part in parts:
            part = part.strip()
            translated = self.category_translations.get(part, part)
            translated_parts.append(translated)
            
        return ' > '.join(translated_parts)
        
    def generate_german_sku(self, name, brand, row_id):
        """生成德语产品SKU"""
        sku_parts = []
        
        if brand and pd.notna(brand):
            brand_clean = re.sub(r'[^A-Za-z0-9]', '', str(brand))[:8].upper()
            sku_parts.append(brand_clean)
            
        if name and pd.notna(name):
            name_clean = re.sub(r'[^A-Za-z0-9]', '', str(name))[:8].upper()
            sku_parts.append(name_clean)
            
        sku_parts.append(str(row_id).zfill(4))
        
        return '-'.join(sku_parts) if sku_parts else f'DE-{row_id:06d}'
        
    def process_images(self, image_data):
        """处理图片URL"""
        if pd.isna(image_data) or image_data == '':
            return ''
            
        image_str = str(image_data).strip()
        
        # 分割多个图片URL
        separators = [',', ';', '|', ' ']
        urls = [image_str]
        
        for sep in separators:
            if sep in image_str:
                urls = image_str.split(sep)
                break
                
        # 清理和验证URL
        valid_urls = []
        for url in urls[:5]:  # 最多5张图片
            url = url.strip()
            if url and ('http' in url or url.startswith('//')):
                # 确保URL格式正确
                if url.startswith('//'):
                    url = 'https:' + url
                valid_urls.append(url)
                
        return ','.join(valid_urls)
        
    def convert_file(self, input_file, output_file=None):
        """转换单个文件"""
        try:
            self.logger.info(f"开始处理文件: {Path(input_file).name}")
            
            # 读取数据
            if str(input_file).endswith('.csv'):
                df = pd.read_csv(input_file, encoding='utf-8-sig')
            else:
                df = pd.read_excel(input_file)
                
            self.logger.info(f"原始数据: {len(df)} 行, {len(df.columns)} 列")
            
            if len(df) == 0:
                self.logger.warning("文件为空")
                return None
                
            # 检测关键字段
            name_col = self.detect_field(df, self.field_mappings['name_fields'])
            price_col = self.detect_field(df, self.field_mappings['price_fields'])
            sale_price_col = self.detect_field(df, self.field_mappings['sale_price_fields'])
            desc_col = self.detect_field(df, self.field_mappings['description_fields'])
            short_desc_col = self.detect_field(df, self.field_mappings['short_desc_fields'])
            category_col = self.detect_field(df, self.field_mappings['category_fields'])
            image_col = self.detect_field(df, self.field_mappings['image_fields'])
            brand_col = self.detect_field(df, self.field_mappings['brand_fields'])
            tags_col = self.detect_field(df, self.field_mappings['tags_fields'])
            
            self.logger.info(f"字段检测结果:")
            self.logger.info(f"  产品名称: {name_col}")
            self.logger.info(f"  常规价格: {price_col}")
            self.logger.info(f"  促销价格: {sale_price_col}")
            self.logger.info(f"  分类: {category_col}")
            self.logger.info(f"  品牌: {brand_col}")
            
            # 转换数据
            wc_data = []
            
            for i, row in df.iterrows():
                wc_row = self.create_woocommerce_row(row, i+1, {
                    'name': name_col,
                    'price': price_col,
                    'sale_price': sale_price_col,
                    'description': desc_col,
                    'short_description': short_desc_col,
                    'category': category_col,
                    'image': image_col,
                    'brand': brand_col,
                    'tags': tags_col
                })
                wc_data.append(wc_row)
                
            # 创建DataFrame
            wc_df = pd.DataFrame(wc_data)
            
            # 确保列顺序
            final_columns = [col for col in self.wc_fields if col in wc_df.columns]
            wc_df = wc_df[final_columns]
            
            # 保存文件
            if output_file is None:
                input_path = Path(input_file)
                output_file = f"woocommerce_{input_path.stem}_de.csv"
                
            wc_df.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            self.logger.info(f"转换完成: {output_file}")
            self.logger.info(f"产品数量: {len(wc_df)}")
            
            return output_file
            
        except Exception as e:
            self.logger.error(f"转换失败: {e}")
            import traceback
            traceback.print_exc()
            return None
            
    def create_woocommerce_row(self, row, row_id, field_map):
        """创建WooCommerce行数据"""
        wc_row = {}
        
        # 基本信息
        wc_row['ID'] = row_id
        wc_row['Type'] = 'simple'
        
        # 产品名称
        name = ''
        if field_map['name'] and pd.notna(row[field_map['name']]):
            name = str(row[field_map['name']]).strip()
        wc_row['Name'] = name if name else f"Product {row_id}"
        
        # 品牌
        brand = ''
        if field_map['brand'] and pd.notna(row[field_map['brand']]):
            brand = str(row[field_map['brand']]).strip()
            
        # SKU
        wc_row['SKU'] = self.generate_german_sku(name, brand, row_id)
        
        # 价格
        regular_price = 0
        if field_map['price'] and pd.notna(row[field_map['price']]):
            regular_price = self.clean_german_price(row[field_map['price']])
        wc_row['Regular price'] = regular_price if regular_price > 0 else ''
        
        # 促销价格
        sale_price = 0
        if field_map['sale_price'] and pd.notna(row[field_map['sale_price']]):
            sale_price = self.clean_german_price(row[field_map['sale_price']])
            if sale_price > 0 and sale_price < regular_price:
                wc_row['Sale price'] = sale_price
            else:
                wc_row['Sale price'] = ''
        else:
            wc_row['Sale price'] = ''
            
        # 描述
        description = ''
        if field_map['description'] and pd.notna(row[field_map['description']]):
            description = self.clean_german_html(row[field_map['description']])
        wc_row['Description'] = description
        
        # 简短描述
        short_desc = ''
        if field_map['short_description'] and pd.notna(row[field_map['short_description']]):
            short_desc = self.clean_german_html(row[field_map['short_description']])
        elif name:
            short_desc = name[:160] + '...' if len(name) > 160 else name
        wc_row['Short description'] = short_desc
        
        # 分类
        category = 'General'
        if field_map['category'] and pd.notna(row[field_map['category']]):
            category = self.translate_category(row[field_map['category']])
        wc_row['Categories'] = category
        
        # 图片
        images = ''
        if field_map['image'] and pd.notna(row[field_map['image']]):
            images = self.process_images(row[field_map['image']])
        wc_row['Images'] = images
        
        # 标签
        tags = ''
        if field_map['tags'] and pd.notna(row[field_map['tags']]):
            tags = str(row[field_map['tags']]).strip()
        elif brand:
            tags = brand
        wc_row['Tags'] = tags
        
        # 品牌属性
        if brand:
            wc_row['Attribute 1 name'] = 'Brand'
            wc_row['Attribute 1 value(s)'] = brand
            wc_row['Attribute 1 visible'] = 1
            wc_row['Attribute 1 global'] = 1
        else:
            wc_row['Attribute 1 name'] = ''
            wc_row['Attribute 1 value(s)'] = ''
            wc_row['Attribute 1 visible'] = 0
            wc_row['Attribute 1 global'] = 0
            
        # 默认设置
        wc_row['Published'] = 1
        wc_row['Is featured?'] = 0
        wc_row['Visibility in catalog'] = 'visible'
        wc_row['Tax status'] = 'taxable'
        wc_row['Tax class'] = ''
        wc_row['In stock?'] = 1
        wc_row['Stock'] = 100
        wc_row['Low stock amount'] = ''
        wc_row['Backorders allowed?'] = 0
        wc_row['Sold individually?'] = 0
        wc_row['Weight (kg)'] = ''
        wc_row['Length (cm)'] = ''
        wc_row['Width (cm)'] = ''
        wc_row['Height (cm)'] = ''
        wc_row['Allow customer reviews?'] = 1
        wc_row['Purchase note'] = ''
        wc_row['Shipping class'] = ''
        wc_row['Date sale price starts'] = ''
        wc_row['Date sale price ends'] = ''
        wc_row['Download limit'] = ''
        wc_row['Download expiry days'] = ''
        wc_row['Parent'] = ''
        wc_row['Grouped products'] = ''
        wc_row['Upsells'] = ''
        wc_row['Cross-sells'] = ''
        wc_row['External URL'] = ''
        wc_row['Button text'] = ''
        wc_row['Position'] = 0
        
        return wc_row

    def batch_convert(self, input_dir, output_dir=None):
        """批量转换目录中的所有文件"""
        input_path = Path(input_dir)

        if not input_path.exists():
            self.logger.error(f"输入目录不存在: {input_dir}")
            return []

        if output_dir is None:
            output_dir = "woocommerce_output_de"

        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        # 查找Excel和CSV文件
        files = list(input_path.glob("*.xlsx")) + list(input_path.glob("*.xls")) + list(input_path.glob("*.csv"))

        if not files:
            self.logger.error("目录中没有找到数据文件")
            return []

        self.logger.info(f"开始批量转换")
        self.logger.info(f"输入目录: {input_dir}")
        self.logger.info(f"输出目录: {output_dir}")
        self.logger.info(f"文件数量: {len(files)}")

        converted_files = []
        total_products = 0
        start_time = datetime.now()

        for i, file_path in enumerate(files, 1):
            self.logger.info(f"\n[{i}/{len(files)}] " + "="*50)

            output_file = output_path / f"woocommerce_{file_path.stem}_de.csv"

            result = self.convert_file(str(file_path), str(output_file))

            if result:
                converted_files.append(result)
                # 统计产品数量
                try:
                    df = pd.read_csv(result, encoding='utf-8-sig')
                    total_products += len(df)
                except:
                    pass

        end_time = datetime.now()
        duration = end_time - start_time

        self.logger.info(f"\n🎉 批量转换完成!")
        self.logger.info(f"成功转换: {len(converted_files)}/{len(files)} 个文件")
        self.logger.info(f"总产品数: {total_products:,}")
        self.logger.info(f"处理时间: {duration}")
        self.logger.info(f"平均速度: {total_products/duration.total_seconds():.1f} 产品/秒")

        return converted_files

    def generate_report(self, converted_files):
        """生成转换报告"""
        if not converted_files:
            return

        report_data = []
        total_products = 0

        for file_path in converted_files:
            try:
                df = pd.read_csv(file_path, encoding='utf-8-sig')
                file_name = Path(file_path).name

                # 统计信息
                product_count = len(df)
                has_price = df['Regular price'].notna().sum()
                has_sale_price = df['Sale price'].notna().sum()
                has_images = df['Images'].notna().sum()
                has_description = df['Description'].notna().sum()

                report_data.append({
                    'File': file_name,
                    'Products': product_count,
                    'With Price': has_price,
                    'With Sale Price': has_sale_price,
                    'With Images': has_images,
                    'With Description': has_description,
                    'Price Coverage': f"{has_price/product_count*100:.1f}%",
                    'Image Coverage': f"{has_images/product_count*100:.1f}%"
                })

                total_products += product_count

            except Exception as e:
                self.logger.error(f"分析文件失败 {file_path}: {e}")

        # 生成报告
        report_df = pd.DataFrame(report_data)
        report_file = f"conversion_report_de_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        report_df.to_csv(report_file, index=False, encoding='utf-8-sig')

        self.logger.info(f"\n📊 转换报告已生成: {report_file}")
        self.logger.info(f"总产品数: {total_products:,}")

        return report_file

def main():
    """主函数"""
    converter = GermanWooCommerceConverter()

    print("🇩🇪 德国数据源WooCommerce转换工具")
    print("="*50)

    if len(sys.argv) > 1:
        input_path = sys.argv[1]

        if Path(input_path).is_file():
            # 单文件转换
            result = converter.convert_file(input_path)
            if result:
                print(f"✅ 转换成功: {result}")
            else:
                print("❌ 转换失败")
        elif Path(input_path).is_dir():
            # 批量转换
            output_dir = sys.argv[2] if len(sys.argv) > 2 else None
            converted_files = converter.batch_convert(input_path, output_dir)

            if converted_files:
                converter.generate_report(converted_files)
            else:
                print("❌ 批量转换失败")
        else:
            print(f"❌ 路径不存在: {input_path}")
    else:
        # 默认处理de目录
        de_dir = "源数据文件/de"
        if Path(de_dir).exists():
            converted_files = converter.batch_convert(de_dir)
            if converted_files:
                converter.generate_report(converted_files)
        else:
            print(f"❌ 默认目录不存在: {de_dir}")
            print("用法: python convert_de_to_woocommerce.py <输入文件/目录> [输出目录]")

if __name__ == "__main__":
    main()
