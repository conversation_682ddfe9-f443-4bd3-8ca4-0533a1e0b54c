#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析Klickparts和Lampenwelt产品数据，提供优化建议
"""

import pandas as pd
import re
from collections import Counter

def analyze_file_structure(file_path, file_name):
    """分析文件基本结构"""
    print(f'📊 {file_name} 数据分析')
    print('='*50)
    
    df = pd.read_csv(file_path, encoding='utf-8-sig')
    
    print(f'总产品数: {len(df):,}')
    print(f'字段数量: {len(df.columns)}')
    print()
    
    # 分析关键字段
    key_fields = ['Name', 'Short description', 'Description', 'Categories', 'Tags', 'Images', 'Regular price', 'Sale price']
    
    print('关键字段分析:')
    for field in key_fields:
        if field in df.columns:
            non_empty = df[field].notna().sum()
            empty = len(df) - non_empty
            coverage = non_empty / len(df) * 100
            print(f'  {field}: {non_empty:,}个有值 ({coverage:.1f}%), {empty:,}个空值')
    
    return df

def analyze_short_descriptions(df, file_name):
    """分析短描述"""
    print(f'\n📝 {file_name} 短描述分析')
    print('-'*40)
    
    short_desc = df['Short description']
    
    # 基本统计
    non_empty = short_desc.notna().sum()
    empty = len(df) - non_empty
    
    print(f'有短描述: {non_empty:,}个 ({non_empty/len(df)*100:.1f}%)')
    print(f'空短描述: {empty:,}个 ({empty/len(df)*100:.1f}%)')
    
    if non_empty > 0:
        # 长度分析
        lengths = [len(str(desc)) for desc in short_desc.dropna()]
        avg_length = sum(lengths) / len(lengths)
        
        print(f'平均长度: {avg_length:.1f} 字符')
        print(f'长度范围: {min(lengths)} - {max(lengths)} 字符')
        
        # 显示示例
        print(f'\n前5个短描述示例:')
        count = 0
        for desc in short_desc.dropna():
            if count >= 5:
                break
            print(f'  {count+1}. {str(desc)[:80]}...')
            count += 1
    
    return non_empty, empty

def analyze_categories(df, file_name):
    """分析分类"""
    print(f'\n🏷️ {file_name} 分类分析')
    print('-'*40)
    
    categories = df['Categories'].dropna()
    
    if len(categories) > 0:
        print(f'有分类的产品: {len(categories):,}个')
        
        # 分析分类层级
        category_levels = []
        for cat in categories.head(100):
            cat_str = str(cat)
            if ' > ' in cat_str:
                levels = len(cat_str.split(' > '))
            else:
                levels = 1
            category_levels.append(levels)
        
        if category_levels:
            avg_levels = sum(category_levels) / len(category_levels)
            print(f'平均分类层级: {avg_levels:.1f}')
        
        # 显示主要分类
        print(f'\n主要分类示例:')
        unique_categories = categories.value_counts().head(10)
        for cat, count in unique_categories.items():
            print(f'  "{cat}" - {count}个产品')
    
    return categories

def analyze_product_names(df, file_name):
    """分析产品名称特点"""
    print(f'\n📦 {file_name} 产品名称分析')
    print('-'*40)
    
    names = df['Name'].dropna()
    
    if len(names) > 0:
        # 长度分析
        name_lengths = [len(str(name)) for name in names]
        avg_length = sum(name_lengths) / len(name_lengths)
        
        print(f'产品名称平均长度: {avg_length:.1f} 字符')
        
        # 关键词分析
        all_words = []
        for name in names.head(1000):  # 分析前1000个
            words = str(name).lower().split()
            all_words.extend(words)
        
        word_counter = Counter(all_words)
        print(f'\n最常见的关键词:')
        for word, count in word_counter.most_common(10):
            if len(word) > 2:  # 过滤短词
                print(f'  "{word}" - 出现{count}次')
        
        # 显示示例
        print(f'\n产品名称示例:')
        for i, name in enumerate(names.head(5)):
            print(f'  {i+1}. {str(name)[:60]}...')
    
    return names

def analyze_pricing(df, file_name):
    """分析价格"""
    print(f'\n💰 {file_name} 价格分析')
    print('-'*40)
    
    regular_prices = df['Regular price'].dropna()
    sale_prices = df['Sale price'].dropna()
    
    if len(regular_prices) > 0:
        # 转换为数值
        price_values = []
        for price in regular_prices:
            try:
                price_val = float(price)
                if price_val > 0:
                    price_values.append(price_val)
            except:
                pass
        
        if price_values:
            avg_price = sum(price_values) / len(price_values)
            min_price = min(price_values)
            max_price = max(price_values)
            
            print(f'常规价格统计:')
            print(f'  平均价格: €{avg_price:.2f}')
            print(f'  价格范围: €{min_price:.2f} - €{max_price:.2f}')
            
            # 价格区间分布
            price_ranges = {
                '€0-50': len([p for p in price_values if 0 < p <= 50]),
                '€50-100': len([p for p in price_values if 50 < p <= 100]),
                '€100-500': len([p for p in price_values if 100 < p <= 500]),
                '€500+': len([p for p in price_values if p > 500])
            }
            
            print(f'\n价格区间分布:')
            for range_name, count in price_ranges.items():
                percentage = count / len(price_values) * 100
                print(f'  {range_name}: {count}个 ({percentage:.1f}%)')
    
    print(f'促销价格: {len(sale_prices):,}个产品有促销价')
    
    return regular_prices, sale_prices

def generate_optimization_suggestions(klickparts_df, lampenwelt_df):
    """生成优化建议"""
    print(f'\n💡 优化建议')
    print('='*60)
    
    print('🔧 Klickparts优化建议:')
    print('-'*30)
    
    # 分析Klickparts特点
    klick_names = klickparts_df['Name'].dropna()
    klick_categories = klickparts_df['Categories'].dropna()
    
    # 检查是否是汽车配件
    auto_keywords = ['auto', 'car', 'motor', 'fahrzeug', 'kfz', 'pkw', 'lkw']
    is_auto_parts = False
    
    for name in klick_names.head(100):
        name_lower = str(name).lower()
        if any(keyword in name_lower for keyword in auto_keywords):
            is_auto_parts = True
            break
    
    if is_auto_parts:
        print('1. 产品类型: 汽车配件/零部件')
        print('2. 短描述优化建议:')
        print('   • 突出兼容性: "适用于[车型/年份]"')
        print('   • 强调质量: "OEM品质标准"')
        print('   • 功能特点: "提升性能/安全性"')
        print('   • 安装便利: "简易安装/即插即用"')
        print()
        print('3. 建议卖点模板:')
        print('   ✓ OEM品质，完美兼容')
        print('   ✓ 提升车辆性能与安全')
        print('   ✓ 专业品质，长期保修')
    else:
        print('1. 需要进一步分析产品类型')
        print('2. 通用优化建议:')
        print('   • 突出产品功能和用途')
        print('   • 强调品质和可靠性')
        print('   • 提供技术规格信息')
    
    print(f'\n🔧 Lampenwelt优化建议:')
    print('-'*30)
    print('1. 产品类型: 照明设备/灯具')
    print('2. 短描述优化建议:')
    print('   • 光效特点: "高亮度/节能LED"')
    print('   • 设计风格: "现代/经典/工业风格"')
    print('   • 应用场景: "客厅/卧室/办公室适用"')
    print('   • 技术优势: "调光功能/长寿命"')
    print()
    print('3. 建议卖点模板:')
    print('   ✓ 高效LED技术，节能环保')
    print('   ✓ 优质光效，营造完美氛围')
    print('   ✓ 精工制作，持久耐用')
    print()
    print('4. 按灯具类型细分:')
    print('   • 吊灯: 强调装饰效果和空间适配')
    print('   • 台灯: 强调护眼和功能性')
    print('   • 射灯: 强调聚光效果和安装便利')
    print('   • LED灯泡: 强调节能和寿命')

def main():
    """主函数"""
    print('🔍 Klickparts & Lampenwelt 产品数据分析')
    print('='*60)
    
    # 文件路径
    klickparts_file = 'woocommerce_output_final/woocommerce_klickparts-de-3-op_final.csv'
    lampenwelt_file = 'woocommerce_output_final/woocommerce_lampenwelt-de_final.csv'
    
    # 分析Klickparts
    klickparts_df = analyze_file_structure(klickparts_file, 'Klickparts')
    analyze_short_descriptions(klickparts_df, 'Klickparts')
    analyze_categories(klickparts_df, 'Klickparts')
    analyze_product_names(klickparts_df, 'Klickparts')
    analyze_pricing(klickparts_df, 'Klickparts')
    
    print('\n' + '='*60)
    
    # 分析Lampenwelt
    lampenwelt_df = analyze_file_structure(lampenwelt_file, 'Lampenwelt')
    analyze_short_descriptions(lampenwelt_df, 'Lampenwelt')
    analyze_categories(lampenwelt_df, 'Lampenwelt')
    analyze_product_names(lampenwelt_df, 'Lampenwelt')
    analyze_pricing(lampenwelt_df, 'Lampenwelt')
    
    # 生成优化建议
    generate_optimization_suggestions(klickparts_df, lampenwelt_df)

if __name__ == "__main__":
    main()
