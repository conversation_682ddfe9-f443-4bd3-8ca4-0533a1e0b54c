#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
产品分类归集脚本 - 根据产品分类对数据进行归集和分组处理
"""

import pandas as pd
import numpy as np
from pathlib import Path
from collections import Counter, defaultdict
import re
import json

class CategoryAggregator:
    def __init__(self):
        self.category_stats = {}
        self.aggregation_results = {}
        
        # 分类标准化映射
        self.category_standardization = {
            # 家具类标准化
            'furniture_keywords': ['furniture', 'chair', 'table', 'desk', 'bed', 'sofa', 'cabinet'],
            'furniture_standard': 'Home & Garden > Furniture',
            
            # 电子产品标准化
            'electronics_keywords': ['electronics', 'computer', 'phone', 'tablet', 'tv', 'monitor'],
            'electronics_standard': 'Electronics',
            
            # 服装类标准化
            'clothing_keywords': ['clothing', 'apparel', 'shirt', 'pants', 'dress', 'jacket'],
            'clothing_standard': 'Clothing & Accessories',
            
            # 工具类标准化
            'tools_keywords': ['tools', 'hardware', 'drill', 'hammer', 'equipment'],
            'tools_standard': 'Tools & Hardware'
        }

    def detect_category_column(self, df: pd.DataFrame):
        """智能检测分类列"""
        category_candidates = []
        
        for col in df.columns:
            col_lower = str(col).lower()
            
            # 直接匹配分类相关词汇
            if any(keyword in col_lower for keyword in ['category', 'categories', 'type', 'class', 'group', 'classification']):
                category_candidates.append((col, 10))  # 高优先级
            
            # 检查列数据特征
            elif df[col].dtype == 'object':
                sample_data = df[col].dropna().head(20)
                if len(sample_data) > 0:
                    # 检查是否包含层级分类符号
                    hierarchical_count = sum(1 for val in sample_data if any(sep in str(val) for sep in ['>', '/', '|', '-']))
                    
                    if hierarchical_count > len(sample_data) * 0.3:  # 30%以上是层级分类
                        category_candidates.append((col, 8))
                    
                    # 检查唯一值比例
                    unique_ratio = len(sample_data.unique()) / len(sample_data)
                    if 0.1 <= unique_ratio <= 0.8:  # 合理的分类唯一值比例
                        category_candidates.append((col, 5))
        
        # 按优先级排序
        category_candidates.sort(key=lambda x: x[1], reverse=True)
        
        return category_candidates[0][0] if category_candidates else None

    def standardize_category_name(self, category: str):
        """标准化分类名称"""
        if pd.isna(category) or category == '':
            return 'Uncategorized'
        
        category_lower = str(category).lower().strip()
        
        # 检查各个标准分类
        for key_prefix in ['furniture', 'electronics', 'clothing', 'tools']:
            keywords = self.category_standardization.get(f'{key_prefix}_keywords', [])
            standard = self.category_standardization.get(f'{key_prefix}_standard', category)
            
            if any(keyword in category_lower for keyword in keywords):
                return standard
        
        # 如果没有匹配，清理并返回原分类
        cleaned = re.sub(r'[^\w\s>/-]', '', str(category))
        cleaned = ' '.join(word.capitalize() for word in cleaned.split())
        
        return cleaned if cleaned else 'Uncategorized'

    def aggregate_by_category(self, df: pd.DataFrame, category_column: str, output_dir: str = None):
        """按分类归集数据"""
        print(f"📊 开始按分类归集数据")
        print(f"分类列: {category_column}")
        print(f"总产品数: {len(df):,}")
        
        if output_dir is None:
            output_dir = "category_aggregated"
        
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # 标准化分类名称
        df_work = df.copy()
        df_work['Standardized_Category'] = df_work[category_column].apply(self.standardize_category_name)
        
        # 按分类分组
        category_groups = df_work.groupby('Standardized_Category')
        
        print(f"\n📂 发现 {len(category_groups)} 个分类:")
        
        aggregation_results = {}
        category_files = []
        
        for category_name, group_df in category_groups:
            print(f"  📁 {category_name}: {len(group_df):,} 个产品")
            
            # 生成分类统计
            category_stats = self.generate_category_stats(group_df, category_name)
            aggregation_results[category_name] = category_stats
            
            # 保存分类数据到单独文件
            safe_category_name = re.sub(r'[^\w\s-]', '_', category_name).replace(' ', '_')
            category_file = output_path / f"{safe_category_name}_{len(group_df)}.csv"
            
            group_df.to_csv(category_file, index=False, encoding='utf-8-sig')
            category_files.append(str(category_file))
            
            print(f"    💾 已保存: {category_file.name}")
        
        # 生成汇总报告
        summary_report = self.generate_summary_report(aggregation_results, len(df))
        
        # 保存汇总报告
        summary_file = output_path / "category_aggregation_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_report, f, ensure_ascii=False, indent=2)
        
        # 生成CSV格式的汇总
        summary_csv = self.create_summary_csv(aggregation_results)
        summary_csv_file = output_path / "category_summary.csv"
        summary_csv.to_csv(summary_csv_file, index=False, encoding='utf-8-sig')
        
        print(f"\n✅ 分类归集完成!")
        print(f"📁 输出目录: {output_dir}")
        print(f"📊 生成文件: {len(category_files)} 个分类文件")
        print(f"📋 汇总报告: {summary_file.name}")
        print(f"📈 汇总CSV: {summary_csv_file.name}")
        
        return {
            'output_directory': str(output_path),
            'category_files': category_files,
            'summary_report': str(summary_file),
            'summary_csv': str(summary_csv_file),
            'categories_count': len(category_groups),
            'total_products': len(df)
        }

    def generate_category_stats(self, category_df: pd.DataFrame, category_name: str):
        """生成单个分类的统计信息"""
        stats = {
            'category_name': category_name,
            'product_count': len(category_df),
            'columns_count': len(category_df.columns)
        }
        
        # 价格统计（如果有价格列）
        price_columns = [col for col in category_df.columns if 'price' in str(col).lower()]
        if price_columns:
            price_col = price_columns[0]
            numeric_prices = pd.to_numeric(category_df[price_col], errors='coerce').dropna()
            
            if len(numeric_prices) > 0:
                stats['price_stats'] = {
                    'min_price': float(numeric_prices.min()),
                    'max_price': float(numeric_prices.max()),
                    'avg_price': float(numeric_prices.mean()),
                    'median_price': float(numeric_prices.median()),
                    'price_count': len(numeric_prices)
                }
        
        # 库存统计（如果有库存列）
        stock_columns = [col for col in category_df.columns if any(keyword in str(col).lower() for keyword in ['stock', 'inventory', 'quantity', 'qty'])]
        if stock_columns:
            stock_col = stock_columns[0]
            numeric_stock = pd.to_numeric(category_df[stock_col], errors='coerce').dropna()
            
            if len(numeric_stock) > 0:
                stats['stock_stats'] = {
                    'total_stock': int(numeric_stock.sum()),
                    'avg_stock': float(numeric_stock.mean()),
                    'products_with_stock': len(numeric_stock[numeric_stock > 0])
                }
        
        # 品牌统计（如果有品牌列）
        brand_columns = [col for col in category_df.columns if 'brand' in str(col).lower()]
        if brand_columns:
            brand_col = brand_columns[0]
            brands = category_df[brand_col].dropna().astype(str)
            brand_counts = Counter(brands)
            
            stats['brand_stats'] = {
                'unique_brands': len(brand_counts),
                'top_brands': dict(brand_counts.most_common(5))
            }
        
        return stats

    def generate_summary_report(self, aggregation_results: dict, total_products: int):
        """生成汇总报告"""
        summary = {
            'aggregation_timestamp': pd.Timestamp.now().isoformat(),
            'total_products': total_products,
            'total_categories': len(aggregation_results),
            'categories': aggregation_results
        }
        
        # 计算分类分布
        category_distribution = {}
        for category, stats in aggregation_results.items():
            product_count = stats['product_count']
            percentage = (product_count / total_products) * 100
            category_distribution[category] = {
                'count': product_count,
                'percentage': round(percentage, 2)
            }
        
        summary['category_distribution'] = category_distribution
        
        # 找出最大和最小分类
        sorted_categories = sorted(aggregation_results.items(), key=lambda x: x[1]['product_count'], reverse=True)
        
        summary['largest_category'] = {
            'name': sorted_categories[0][0],
            'count': sorted_categories[0][1]['product_count']
        }
        
        summary['smallest_category'] = {
            'name': sorted_categories[-1][0],
            'count': sorted_categories[-1][1]['product_count']
        }
        
        return summary

    def create_summary_csv(self, aggregation_results: dict):
        """创建CSV格式的汇总表"""
        summary_data = []
        
        for category, stats in aggregation_results.items():
            row = {
                'Category': category,
                'Product_Count': stats['product_count'],
                'Columns_Count': stats['columns_count']
            }
            
            # 添加价格信息
            if 'price_stats' in stats:
                price_stats = stats['price_stats']
                row.update({
                    'Min_Price': price_stats['min_price'],
                    'Max_Price': price_stats['max_price'],
                    'Avg_Price': round(price_stats['avg_price'], 2),
                    'Median_Price': price_stats['median_price']
                })
            
            # 添加库存信息
            if 'stock_stats' in stats:
                stock_stats = stats['stock_stats']
                row.update({
                    'Total_Stock': stock_stats['total_stock'],
                    'Avg_Stock': round(stock_stats['avg_stock'], 2),
                    'Products_With_Stock': stock_stats['products_with_stock']
                })
            
            # 添加品牌信息
            if 'brand_stats' in stats:
                brand_stats = stats['brand_stats']
                row['Unique_Brands'] = brand_stats['unique_brands']
                
                # 添加前3个品牌
                top_brands = list(brand_stats['top_brands'].items())[:3]
                for i, (brand, count) in enumerate(top_brands, 1):
                    row[f'Top_Brand_{i}'] = f"{brand} ({count})"
            
            summary_data.append(row)
        
        return pd.DataFrame(summary_data).sort_values('Product_Count', ascending=False)

    def aggregate_file(self, input_file: str, output_dir: str = None):
        """处理单个文件的分类归集"""
        try:
            print(f"📁 处理文件: {Path(input_file).name}")
            
            # 读取数据
            if input_file.endswith('.csv'):
                df = pd.read_csv(input_file, encoding='utf-8-sig')
            else:
                df = pd.read_excel(input_file)
            
            print(f"📊 数据概况: {len(df)} 行, {len(df.columns)} 列")
            
            # 检测分类列
            category_column = self.detect_category_column(df)
            
            if not category_column:
                print("❌ 未找到分类列")
                return None
            
            print(f"🎯 检测到分类列: {category_column}")
            
            # 执行归集
            if output_dir is None:
                input_path = Path(input_file)
                output_dir = f"aggregated_{input_path.stem}"
            
            result = self.aggregate_by_category(df, category_column, output_dir)
            
            return result
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def batch_aggregate(self, input_directory: str, output_base_dir: str = None):
        """批量处理目录中的文件"""
        input_dir = Path(input_directory)
        
        if not input_dir.exists():
            print(f"❌ 输入目录不存在: {input_directory}")
            return []
        
        data_files = list(input_dir.glob("*.csv")) + list(input_dir.glob("*.xlsx"))
        
        if not data_files:
            print(f"❌ 目录中没有数据文件")
            return []
        
        print(f"🚀 批量分类归集")
        print(f"输入目录: {input_directory}")
        print(f"文件数量: {len(data_files)}")
        
        if output_base_dir is None:
            output_base_dir = "batch_aggregated"
        
        results = []
        
        for i, file_path in enumerate(data_files, 1):
            print(f"\n[{i}/{len(data_files)}] " + "="*50)
            
            file_output_dir = Path(output_base_dir) / f"aggregated_{file_path.stem}"
            result = self.aggregate_file(str(file_path), str(file_output_dir))
            
            if result:
                results.append(result)
        
        print(f"\n🎉 批量归集完成!")
        print(f"成功处理: {len(results)} 个文件")
        
        return results

def main():
    """主函数"""
    aggregator = CategoryAggregator()
    
    print("📊 产品分类归集工具")
    print("="*50)
    
    print("📋 使用方法:")
    print("1. 单文件归集:")
    print("   aggregator.aggregate_file('input.csv', 'output_dir')")
    print("2. 批量归集:")
    print("   aggregator.batch_aggregate('input_directory', 'output_base_dir')")

if __name__ == "__main__":
    main()
