#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析Segmueller产品短描述和卖点
"""

import pandas as pd
import re
from collections import Counter

def analyze_segmueller_descriptions():
    print('🔍 Segmueller产品短描述深度分析')
    print('='*60)
    
    file_path = 'woocommerce_output_final/woocommerce_segmueller-de-图片清理小图_final.csv'
    df = pd.read_csv(file_path, encoding='utf-8-sig')
    
    short_descriptions = df['Short description'].dropna()
    
    print(f'总产品数: {len(df):,}')
    print(f'有短描述产品数: {len(short_descriptions):,}')
    print()
    
    # 分析描述结构
    print('📋 描述结构分析')
    print('-' * 40)
    
    # 提取产品信息部分（✓之前的部分）
    product_info_parts = []
    selling_points_parts = []
    
    for desc in short_descriptions.head(100):  # 分析前100个
        desc_str = str(desc)
        
        # 分割产品信息和卖点
        if '✓' in desc_str:
            parts = desc_str.split('✓', 1)
            product_info = parts[0].strip()
            selling_points = '✓' + parts[1] if len(parts) > 1 else ''
        else:
            product_info = desc_str
            selling_points = ''
        
        product_info_parts.append(product_info)
        selling_points_parts.append(selling_points)
    
    # 分析产品信息部分
    print('产品信息部分示例:')
    for i, info in enumerate(product_info_parts[:5]):
        print(f'{i+1}. {info}')
    print()
    
    # 分析卖点部分
    print('🎯 卖点分析')
    print('-' * 40)
    
    # 提取所有卖点
    all_selling_points = []
    for selling_point in selling_points_parts:
        if selling_point:
            # 分割各个卖点
            points = selling_point.split('✓')
            for point in points:
                point = point.strip()
                if point:
                    all_selling_points.append(point)
    
    # 统计卖点频率
    selling_point_counter = Counter(all_selling_points)
    
    print('最常见的卖点:')
    for point, count in selling_point_counter.most_common(10):
        print(f'  "{point}" - 出现{count}次')
    print()
    
    # 分析描述模式
    print('📝 描述模式分析')
    print('-' * 40)
    
    patterns = {
        '品牌模式': 0,  # 包含"von [品牌]"
        '尺寸模式': 0,  # 包含尺寸信息
        '颜色模式': 0,  # 包含颜色信息
        '材质模式': 0,  # 包含材质信息
    }
    
    for info in product_info_parts:
        if ' von ' in info:
            patterns['品牌模式'] += 1
        if re.search(r'\d+\s*x\s*\d+', info):
            patterns['尺寸模式'] += 1
        if any(color in info.lower() for color in ['schwarz', 'weiß', 'grau', 'rot', 'blau']):
            patterns['颜色模式'] += 1
        if any(material in info.lower() for material in ['holz', 'leder', 'metall', 'stoff']):
            patterns['材质模式'] += 1
    
    for pattern, count in patterns.items():
        percentage = count / len(product_info_parts) * 100
        print(f'{pattern}: {count}个 ({percentage:.1f}%)')
    print()
    
    # 分析长度分布
    print('📏 长度分析')
    print('-' * 40)
    
    lengths = [len(str(desc)) for desc in short_descriptions.head(1000)]
    
    length_ranges = {
        '50-100字符': len([l for l in lengths if 50 <= l < 100]),
        '100-150字符': len([l for l in lengths if 100 <= l < 150]),
        '150-200字符': len([l for l in lengths if 150 <= l < 200]),
        '200+字符': len([l for l in lengths if l >= 200])
    }
    
    for range_name, count in length_ranges.items():
        percentage = count / len(lengths) * 100
        print(f'{range_name}: {count}个 ({percentage:.1f}%)')
    print()
    
    # 生成优化建议
    print('💡 优化建议')
    print('='*60)
    
    print('1. 卖点优化建议:')
    print('   当前卖点分析:')
    for point, count in selling_point_counter.most_common(5):
        print(f'   • "{point}" - 使用频率高，但可能过于通用')
    
    print()
    print('2. 结构优化建议:')
    print('   • 产品信息部分结构良好，包含品牌、型号、尺寸、颜色等')
    print('   • 卖点部分过于标准化，缺乏产品特色')
    print()
    
    print('3. 具体优化方案:')
    print('   A. 差异化卖点:')
    print('      - 根据产品类别定制卖点')
    print('      - 突出产品独特功能和优势')
    print('      - 减少通用性卖点，增加针对性卖点')
    print()
    
    print('   B. 卖点重新设计:')
    print('      当前: ✓ Tiefpreis-Gewährleistung ✓ rasche Versand ✓ Alles fürs Einrichten')
    print('      建议: ✓ [产品特色] ✓ [功能优势] ✓ [品质保证]')
    print()
    
    print('   C. 按产品类别优化:')
    print('      • 家具类: 强调舒适性、耐用性、设计感')
    print('      • 床具类: 强调睡眠质量、健康材质、人体工学')
    print('      • 储物类: 强调收纳能力、空间利用、实用性')
    print()
    
    return selling_point_counter, product_info_parts

def generate_optimized_selling_points():
    """生成优化的卖点建议"""
    print('🚀 优化卖点方案')
    print('='*60)
    
    # 按产品类别定制卖点
    category_selling_points = {
        'Sessel': [
            '✓ 人体工学设计',
            '✓ 高品质材料',
            '✓ 舒适体验保证'
        ],
        'Bett': [
            '✓ 优质睡眠体验',
            '✓ 健康环保材质',
            '✓ 稳固耐用结构'
        ],
        'Schrank': [
            '✓ 大容量收纳',
            '✓ 空间优化设计',
            '✓ 实用功能齐全'
        ],
        'Sofa': [
            '✓ 舒适坐感体验',
            '✓ 时尚设计风格',
            '✓ 耐用品质保证'
        ],
        'Tisch': [
            '✓ 稳固实用设计',
            '✓ 多功能应用',
            '✓ 精工制作工艺'
        ]
    }
    
    print('按产品类别的优化卖点:')
    for category, points in category_selling_points.items():
        print(f'\\n{category}类产品:')
        for point in points:
            print(f'  {point}')
    
    print('\\n通用优化原则:')
    print('1. 第一个卖点: 突出产品核心功能/特色')
    print('2. 第二个卖点: 强调品质/材料/工艺')
    print('3. 第三个卖点: 提供服务/保证承诺')
    
    return category_selling_points

if __name__ == "__main__":
    selling_points, product_info = analyze_segmueller_descriptions()
    optimized_points = generate_optimized_selling_points()
