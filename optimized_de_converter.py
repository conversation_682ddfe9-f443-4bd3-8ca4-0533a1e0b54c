#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的德国数据源WooCommerce转换脚本
结合原脚本功能保留检查清单和通用规则，确保数据完整性和准确性
"""

import pandas as pd
import numpy as np
from pathlib import Path
import re
import sys
import html
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

class OptimizedGermanConverter:
    def __init__(self):
        """初始化转换器，严格遵循原脚本规则"""
        self.setup_logging()
        
        # 德语字段映射 - 基于通用规则扩展
        self.field_mappings = {
            'name_fields': ['title', 'post_title', 'Name', 'product_name', 'titel'],
            'price_fields': ['price', 'regular_price', 'price/100', 'cost', 'preis'],
            'description_fields': ['description', 'detail', 'post_content', 'desc', 'beschreibung'],
            'short_desc_fields': ['description s', 'post_excerpt', 'short description', 'short_description', 'kurzbeschreibung'],
            'category_fields': ['category', 'cate', 'tax:product_cat', 'Categories', 'kategorie'],
            'image_fields': ['image', 'images', 'image0', 'featured', 'photo', 'bild'],
            'sku_fields': ['sku', 'SKU', 'ID', 'product_id', 'artikelnummer'],
            'brand_fields': ['Brand', 'attribute:Brand', 'brand', 'manufacturer', 'Marke', 'Hersteller'],
            'mpn_fields': ['mpn', 'MPN', 'MFG', 'model', 'artikelnummer'],
            'upc_fields': ['UPC', 'upc', 'barcode', 'ean', 'EAN'],
            'tags_fields': ['tags', 'schlagwörter', 'keywords', 'stichwörter']
        }
        
        # WooCommerce默认值 - 严格按照原脚本规则
        self.wc_defaults = {
            'Type': 'simple',
            'Published': 1,
            'Is featured?': 0,
            'Visibility in catalog': 'visible',
            'In stock?': 1,
            'Stock': 100,
            'Backorders allowed?': 0,  # 重要：必须为0，与原脚本一致
            'Sold individually?': 0,
            'Allow customer reviews?': 1,
            'Tax status': 'taxable',
            'Tax class': '',
            'Low stock amount': '',
            'Weight (kg)': '',
            'Length (cm)': '',
            'Width (cm)': '',
            'Height (cm)': '',
            'Purchase note': '',
            'Shipping class': '',
            'Date sale price starts': '',
            'Date sale price ends': '',
            'Download limit': '',
            'Download expiry days': '',
            'Parent': '',
            'Grouped products': '',
            'Upsells': '',
            'Cross-sells': '',
            'External URL': '',
            'Button text': '',
            'Position': 0
        }
        
        # HTML标签智能替换规则 - 基于德国数据特点
        self.html_replacements = {
            r'<font[^>]*>': '<span>',
            r'</font>': '</span>',
            r'<center[^>]*>': '<div style="text-align: center;">',
            r'</center>': '</div>',
            r'<h1[^>]*>': '<h3>',  # SEO优化，h1降级为h3
            r'</h1>': '</h3>',
            r'<h2[^>]*>': '<h3>',  # SEO优化，h2降级为h3
            r'</h2>': '</h3>',
            # 德语特有的清理
            r'<span[^>]*data-ui-id="page-title-wrapper"[^>]*>': '<span>',
            r'<section[^>]*class="attribute-specs__group"[^>]*>': '<div>',
            r'</section>': '</div>',
        }
        
        # 危险标签列表
        self.dangerous_tags = ['script', 'style', 'iframe', 'noscript', 'object', 'embed']
        
        # 分类分隔符处理
        self.category_separators = [
            ('|||', ' > '),
            ('>', ' > '),
            (',', ' > '),
            (';', ' > '),
        ]
        
        # 图片分隔符
        self.image_separators = ['|||', '|', ',', ';', '\n']
        
    def setup_logging(self):
        """设置日志记录"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('optimized_de_conversion.log', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def detect_field(self, df: pd.DataFrame, field_list: List[str]) -> Optional[str]:
        """智能字段检测"""
        for field in field_list:
            for col in df.columns:
                if field.lower() == col.lower() or field.lower() in col.lower():
                    return col
        return None
        
    def clean_german_price(self, price_value: Any) -> float:
        """德语价格格式清理 - 保持原脚本逻辑"""
        if pd.isna(price_value) or price_value == '':
            return 0.0
            
        price_str = str(price_value).strip()
        
        # 移除货币符号
        price_str = re.sub(r'[€$£¥\s]', '', price_str)
        
        # 处理德语数字格式
        if ',' in price_str and '.' in price_str:
            # 1.234,56 -> 1234.56
            price_str = price_str.replace('.', '').replace(',', '.')
        elif ',' in price_str:
            # 判断逗号是否为小数分隔符
            parts = price_str.split(',')
            if len(parts) == 2 and len(parts[1]) <= 2:
                price_str = price_str.replace(',', '.')
            else:
                price_str = price_str.replace(',', '')
                
        try:
            return float(price_str)
        except ValueError:
            return 0.0
            
    def calculate_regular_price(self, original_price: float) -> float:
        """标准价格调整逻辑 - 严格按照通用规则"""
        if original_price < 3:
            discount = 0
        elif original_price <= 10:
            discount = 1
        elif original_price <= 50:
            discount = 2
        else:
            discount = 3
            
        return round(max(original_price - discount, 0.01), 2)
        
    def calculate_sale_price(self, regular_price: float) -> float:
        """标准促销价格逻辑 - 严格按照通用规则"""
        price_tiers = [
            (50, 0.56), (100, 0.5), (150, 0.45), (200, 0.42),
            (250, 0.36), (300, 0.33), (350, 0.31), (500, 0.26),
            (750, 0.24), (1000, 0.22), (2000, 0.2), (float('inf'), 0.15)
        ]
        
        for threshold, multiplier in price_tiers:
            if regular_price <= threshold:
                return round(regular_price * multiplier, 2)
                
        return round(regular_price * 0.15, 2)  # 默认15%折扣
        
    def clean_german_html(self, html_content: Any) -> str:
        """智能HTML清理 - 针对德国数据优化"""
        if pd.isna(html_content) or html_content == '':
            return ''
            
        content = str(html_content)
        
        # 1. 移除危险标签
        for tag in self.dangerous_tags:
            pattern = f'<{tag}[^>]*>.*?</{tag}>'
            content = re.sub(pattern, '', content, flags=re.DOTALL | re.IGNORECASE)
            
        # 2. 智能标签替换
        for pattern, replacement in self.html_replacements.items():
            content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
            
        # 3. 移除危险事件处理器 - 精确匹配避免误删德语单词
        event_patterns = [
            r'\s+on(click|load|mouse\w+|key\w+|focus|blur|change|submit)\s*=\s*["\'][^"\']*["\']',
            r'\s+on(click|load|mouse\w+|key\w+|focus|blur|change|submit)\s*=\s*[^>\s]+',
        ]
        for pattern in event_patterns:
            content = re.sub(pattern, '', content, flags=re.IGNORECASE)
            
        # 4. 清理多余空白但保留德语字符
        content = re.sub(r'\s+', ' ', content).strip()
        
        # 5. HTML实体解码
        content = html.unescape(content)
        
        return content
        
    def process_categories(self, category_str: Any) -> str:
        """标准分类处理 - 按照通用规则"""
        if pd.isna(category_str) or category_str == '':
            return 'General'
            
        category = str(category_str).strip()
        
        # 应用分隔符转换
        for old_sep, new_sep in self.category_separators:
            category = category.replace(old_sep, new_sep)
            
        # 分割并清理各级分类
        parts = [part.strip() for part in category.split(' > ') if part.strip()]
        
        # 去重并限制层级数量（最多4级）
        unique_parts = []
        for part in parts[:4]:
            if part not in unique_parts:
                unique_parts.append(part)
                
        return ' > '.join(unique_parts) if unique_parts else 'General'
        
    def process_images(self, image_str: Any) -> str:
        """标准图片处理 - 按照通用规则"""
        if pd.isna(image_str) or image_str == '':
            return ''
            
        image_content = str(image_str).strip()
        
        # 提取所有可能的URL
        urls = []
        
        # 尝试各种分隔符
        for separator in self.image_separators:
            if separator in image_content:
                urls = image_content.split(separator)
                break
        else:
            urls = [image_content]
            
        # 清理和验证URL
        valid_urls = []
        for url in urls[:5]:  # 最多5张图片
            url = url.strip()
            if url and ('http' in url or url.startswith('//')):
                if url.startswith('//'):
                    url = 'https:' + url
                if url not in valid_urls:  # 去重
                    valid_urls.append(url)
                    
        return ','.join(valid_urls)
        
    def generate_sku(self, name: str, brand: str = "", original_id: str = "", prefix: str = "DE") -> str:
        """标准SKU生成逻辑 - 按照通用规则"""
        # 优先级1: 使用原始ID
        if original_id:
            return f"{prefix}-{original_id}"
            
        # 优先级2: 基于品牌+产品名
        sku_parts = []
        
        if brand:
            brand_clean = re.sub(r'[^A-Za-z0-9]', '', str(brand))[:8].upper()
            sku_parts.append(brand_clean)
            
        if name:
            name_clean = re.sub(r'[^A-Za-z0-9]', '', str(name))[:8].upper()
            sku_parts.append(name_clean)
            
        if not sku_parts:
            return f"{prefix}-PRODUCT"
            
        sku = '-'.join(sku_parts)
        return sku[:20]  # 限制长度
        
    def generate_description(self, row: pd.Series, field_map: Dict[str, str]) -> str:
        """标准描述生成逻辑 - 按优先级处理"""
        # 按优先级查找描述
        desc_fields = ['description', 'detail', 'post_content', 'desc']
        
        for field_key in desc_fields:
            if field_key in field_map and field_map[field_key]:
                col_name = field_map[field_key]
                if pd.notna(row[col_name]):
                    desc = self.clean_german_html(row[col_name])
                    if len(desc.strip()) > 10:  # 确保有意义的描述
                        return desc
                        
        # 如果没有描述，基于产品名生成
        if field_map.get('name') and pd.notna(row[field_map['name']]):
            name = str(row[field_map['name']]).strip()
            return f"<p>{name}</p>"
            
        return ""

    def create_woocommerce_product(self, row: pd.Series, row_id: int, field_map: Dict[str, str]) -> Dict[str, Any]:
        """创建WooCommerce产品数据 - 确保数据完整性和准确性"""
        wc_product = {}

        # 基本信息
        wc_product['ID'] = row_id
        wc_product['Type'] = self.wc_defaults['Type']

        # 产品名称 - 确保不为空
        name = ""
        if field_map.get('name') and pd.notna(row[field_map['name']]):
            name = str(row[field_map['name']]).strip()
        wc_product['Name'] = name if name else f"Product {row_id}"

        # 品牌信息
        brand = ""
        if field_map.get('brand') and pd.notna(row[field_map['brand']]):
            brand = str(row[field_map['brand']]).strip()

        # SKU生成
        original_id = ""
        if field_map.get('sku') and pd.notna(row[field_map['sku']]):
            original_id = str(row[field_map['sku']]).strip()
        wc_product['SKU'] = self.generate_sku(name, brand, original_id)

        # 价格处理 - 严格按照规则
        regular_price = 0.0
        if field_map.get('price') and pd.notna(row[field_map['price']]):
            original_price = self.clean_german_price(row[field_map['price']])
            if original_price > 0:
                regular_price = self.calculate_regular_price(original_price)

        wc_product['Regular price'] = regular_price if regular_price > 0 else ''

        # 促销价格 - 如果有原始促销价则使用，否则计算
        sale_price = ""
        if field_map.get('sale_price') and pd.notna(row[field_map['sale_price']]):
            original_sale = self.clean_german_price(row[field_map['sale_price']])
            if original_sale > 0 and original_sale < regular_price:
                sale_price = original_sale
        elif regular_price > 0:
            # 按照通用规则计算促销价
            calculated_sale = self.calculate_sale_price(regular_price)
            if calculated_sale < regular_price:
                sale_price = calculated_sale

        wc_product['Sale price'] = sale_price

        # 描述处理
        wc_product['Description'] = self.generate_description(row, field_map)

        # 简短描述
        short_desc = ""
        if field_map.get('short_description') and pd.notna(row[field_map['short_description']]):
            short_desc = self.clean_german_html(row[field_map['short_description']])
        elif name:
            # 基于产品名生成简短描述
            short_desc = name[:160] + '...' if len(name) > 160 else name
        wc_product['Short description'] = short_desc

        # 分类处理
        category = 'General'
        if field_map.get('category') and pd.notna(row[field_map['category']]):
            category = self.process_categories(row[field_map['category']])
        wc_product['Categories'] = category

        # 图片处理
        images = ""
        if field_map.get('image') and pd.notna(row[field_map['image']]):
            images = self.process_images(row[field_map['image']])
        wc_product['Images'] = images

        # 标签处理
        tags = ""
        if field_map.get('tags') and pd.notna(row[field_map['tags']]):
            tags_str = str(row[field_map['tags']]).strip()
            # 处理德语标签分隔符
            tags = tags_str.replace(',', ', ').replace(';', ', ')
        elif brand:
            tags = brand
        wc_product['Tags'] = tags

        # 属性处理 - 如果没有属性值则留空
        # 品牌属性
        if brand:
            wc_product['Attribute 1 name'] = 'Brand'
            wc_product['Attribute 1 value(s)'] = brand
            wc_product['Attribute 1 visible'] = 1
            wc_product['Attribute 1 global'] = 1
        else:
            wc_product['Attribute 1 name'] = ''
            wc_product['Attribute 1 value(s)'] = ''
            wc_product['Attribute 1 visible'] = 0
            wc_product['Attribute 1 global'] = 0

        # MPN属性
        mpn = ""
        if field_map.get('mpn') and pd.notna(row[field_map['mpn']]):
            mpn = str(row[field_map['mpn']]).strip()

        if mpn:
            wc_product['Attribute 2 name'] = 'MPN'
            wc_product['Attribute 2 value(s)'] = mpn
            wc_product['Attribute 2 visible'] = 1
            wc_product['Attribute 2 global'] = 0
        else:
            wc_product['Attribute 2 name'] = ''
            wc_product['Attribute 2 value(s)'] = ''
            wc_product['Attribute 2 visible'] = 0
            wc_product['Attribute 2 global'] = 0

        # 应用所有默认值
        for key, value in self.wc_defaults.items():
            if key not in wc_product:
                wc_product[key] = value

        return wc_product

    def convert_file(self, input_file: str, output_file: str = None) -> Optional[str]:
        """转换单个文件 - 确保数据完整性"""
        try:
            self.logger.info(f"开始处理文件: {Path(input_file).name}")

            # 读取数据
            if str(input_file).endswith('.csv'):
                df = pd.read_csv(input_file, encoding='utf-8-sig')
            else:
                df = pd.read_excel(input_file)

            self.logger.info(f"原始数据: {len(df)} 行, {len(df.columns)} 列")

            if len(df) == 0:
                self.logger.warning("文件为空")
                return None

            # 智能字段检测
            field_map = {}
            for field_type, field_list in self.field_mappings.items():
                detected_field = self.detect_field(df, field_list)
                field_map[field_type.replace('_fields', '')] = detected_field

            # 记录检测结果
            self.logger.info("字段检测结果:")
            for field_type, field_name in field_map.items():
                if field_name:
                    self.logger.info(f"  {field_type}: {field_name}")

            # 转换数据
            wc_products = []
            total_rows = len(df)

            for index, row in df.iterrows():
                try:
                    wc_product = self.create_woocommerce_product(row, index + 1, field_map)
                    wc_products.append(wc_product)

                    # 进度显示 - 按照原脚本规则
                    if (index + 1) % 1000 == 0:
                        self.logger.info(f"已处理 {index + 1}/{total_rows} 行...")

                except Exception as e:
                    self.logger.error(f"处理第 {index + 1} 行时出错: {e}")
                    continue

            # 创建DataFrame
            wc_df = pd.DataFrame(wc_products)

            # 确保列顺序正确
            wc_columns = [
                'ID', 'Type', 'SKU', 'Name', 'Published', 'Is featured?', 'Visibility in catalog',
                'Short description', 'Description', 'Date sale price starts', 'Date sale price ends',
                'Tax status', 'Tax class', 'In stock?', 'Stock', 'Low stock amount', 'Backorders allowed?',
                'Sold individually?', 'Weight (kg)', 'Length (cm)', 'Width (cm)', 'Height (cm)',
                'Allow customer reviews?', 'Purchase note', 'Sale price', 'Regular price', 'Categories',
                'Tags', 'Shipping class', 'Images', 'Download limit', 'Download expiry days', 'Parent',
                'Grouped products', 'Upsells', 'Cross-sells', 'External URL', 'Button text', 'Position',
                'Attribute 1 name', 'Attribute 1 value(s)', 'Attribute 1 visible', 'Attribute 1 global',
                'Attribute 2 name', 'Attribute 2 value(s)', 'Attribute 2 visible', 'Attribute 2 global'
            ]

            # 确保所有列都存在
            for col in wc_columns:
                if col not in wc_df.columns:
                    wc_df[col] = ''

            wc_df = wc_df[wc_columns]

            # 保存文件
            if output_file is None:
                input_path = Path(input_file)
                output_file = f"woocommerce_{input_path.stem}_optimized.csv"

            wc_df.to_csv(output_file, index=False, encoding='utf-8-sig')

            self.logger.info(f"转换完成: {output_file}")
            self.logger.info(f"产品数量: {len(wc_df)}")
            self.logger.info(f"数据完整性: {len(wc_df)}/{total_rows} = {len(wc_df)/total_rows*100:.1f}%")

            return output_file

        except Exception as e:
            self.logger.error(f"转换失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def batch_convert(self, input_dir: str, output_dir: str = None) -> List[str]:
        """批量转换目录中的所有文件"""
        input_path = Path(input_dir)

        if not input_path.exists():
            self.logger.error(f"输入目录不存在: {input_dir}")
            return []

        if output_dir is None:
            output_dir = "woocommerce_output_optimized"

        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        # 查找文件 - 支持Excel和CSV
        files = list(input_path.glob("*.xlsx")) + list(input_path.glob("*.xls")) + list(input_path.glob("*.csv"))

        if not files:
            self.logger.error("目录中没有找到数据文件")
            return []

        self.logger.info(f"开始批量转换")
        self.logger.info(f"输入目录: {input_dir}")
        self.logger.info(f"输出目录: {output_dir}")
        self.logger.info(f"文件数量: {len(files)}")

        converted_files = []
        total_products = 0
        start_time = datetime.now()

        for i, file_path in enumerate(files, 1):
            self.logger.info(f"\n[{i}/{len(files)}] " + "="*50)

            output_file = output_path / f"woocommerce_{file_path.stem}_optimized.csv"

            result = self.convert_file(str(file_path), str(output_file))

            if result:
                converted_files.append(result)
                # 统计产品数量
                try:
                    df = pd.read_csv(result, encoding='utf-8-sig')
                    total_products += len(df)
                except:
                    pass

        end_time = datetime.now()
        duration = end_time - start_time

        self.logger.info(f"\n🎉 批量转换完成!")
        self.logger.info(f"成功转换: {len(converted_files)}/{len(files)} 个文件")
        self.logger.info(f"总产品数: {total_products:,}")
        self.logger.info(f"处理时间: {duration}")
        if duration.total_seconds() > 0:
            self.logger.info(f"平均速度: {total_products/duration.total_seconds():.1f} 产品/秒")

        return converted_files

    def validate_conversion(self, source_file: str, converted_file: str) -> Dict[str, Any]:
        """验证转换质量 - 确保数据完整性"""
        validation_result = {
            'source_rows': 0,
            'converted_rows': 0,
            'data_integrity': 0.0,
            'issues': []
        }

        try:
            # 读取源文件和转换文件
            if source_file.endswith('.xlsx'):
                source_df = pd.read_excel(source_file)
            else:
                source_df = pd.read_csv(source_file, encoding='utf-8-sig')

            converted_df = pd.read_csv(converted_file, encoding='utf-8-sig')

            validation_result['source_rows'] = len(source_df)
            validation_result['converted_rows'] = len(converted_df)
            validation_result['data_integrity'] = len(converted_df) / len(source_df) * 100

            # 检查必需字段
            required_fields = ['Name', 'SKU', 'Regular price']
            for field in required_fields:
                empty_count = converted_df[field].isna().sum()
                if empty_count > 0:
                    validation_result['issues'].append(f"{field}字段有{empty_count}个空值")

            # 检查SKU唯一性
            sku_duplicates = converted_df['SKU'].duplicated().sum()
            if sku_duplicates > 0:
                validation_result['issues'].append(f"发现{sku_duplicates}个重复SKU")

        except Exception as e:
            validation_result['issues'].append(f"验证失败: {e}")

        return validation_result

def main():
    """主函数"""
    converter = OptimizedGermanConverter()

    print("🇩🇪 优化德国数据源WooCommerce转换工具")
    print("严格遵循原脚本规则，确保数据完整性和准确性")
    print("="*60)

    if len(sys.argv) > 1:
        input_path = sys.argv[1]

        if Path(input_path).is_file():
            # 单文件转换
            result = converter.convert_file(input_path)
            if result:
                print(f"✅ 转换成功: {result}")

                # 验证转换质量
                validation = converter.validate_conversion(input_path, result)
                print(f"📊 数据完整性: {validation['data_integrity']:.1f}%")
                if validation['issues']:
                    print("⚠️  发现问题:")
                    for issue in validation['issues']:
                        print(f"  - {issue}")
            else:
                print("❌ 转换失败")

        elif Path(input_path).is_dir():
            # 批量转换
            output_dir = sys.argv[2] if len(sys.argv) > 2 else None
            converted_files = converter.batch_convert(input_path, output_dir)

            if converted_files:
                print(f"\n📊 转换质量验证:")
                total_integrity = 0
                for converted_file in converted_files:
                    source_file = None
                    # 找到对应的源文件
                    file_stem = Path(converted_file).stem.replace('woocommerce_', '').replace('_optimized', '')
                    for ext in ['.xlsx', '.xls', '.csv']:
                        potential_source = Path(input_path) / f"{file_stem}{ext}"
                        if potential_source.exists():
                            source_file = str(potential_source)
                            break

                    if source_file:
                        validation = converter.validate_conversion(source_file, converted_file)
                        total_integrity += validation['data_integrity']
                        print(f"  {Path(converted_file).name}: {validation['data_integrity']:.1f}%")

                avg_integrity = total_integrity / len(converted_files)
                print(f"\n🎯 平均数据完整性: {avg_integrity:.1f}%")
            else:
                print("❌ 批量转换失败")
        else:
            print(f"❌ 路径不存在: {input_path}")
    else:
        # 默认处理de目录
        de_dir = "源数据文件/de"
        if Path(de_dir).exists():
            converted_files = converter.batch_convert(de_dir)
            if converted_files:
                print("✅ 默认批量转换完成")
        else:
            print(f"❌ 默认目录不存在: {de_dir}")
            print("用法: python optimized_de_converter.py <输入文件/目录> [输出目录]")

if __name__ == "__main__":
    main()
